"use strict";
cc._RF.push(module, 'a5fb7yX0MxJzJhk2Z9NhWXz', 'GridAnimationTest');
// scripts/test/GridAnimationTest.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var GridAnimationTest = /** @class */ (function (_super) {
    __extends(GridAnimationTest, _super);
    function GridAnimationTest() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.testSquareAnimationBtn = null;
        _this.testHexAnimationBtn = null;
        _this.resetGridsBtn = null;
        _this.statusLabel = null;
        return _this;
    }
    GridAnimationTest.prototype.onLoad = function () {
        // 设置按钮点击事件
        if (this.testSquareAnimationBtn) {
            this.testSquareAnimationBtn.node.on('click', this.testSquareGridAnimation, this);
        }
        if (this.testHexAnimationBtn) {
            this.testHexAnimationBtn.node.on('click', this.testHexGridAnimation, this);
        }
        if (this.resetGridsBtn) {
            this.resetGridsBtn.node.on('click', this.resetAllGrids, this);
        }
    };
    /**
     * 测试四边形格子消失动画
     */
    GridAnimationTest.prototype.testSquareGridAnimation = function () {
        this.updateStatus("测试四边形格子消失动画...");
        // 获取棋盘控制器实例
        var chessBoardController = window.chessBoardController;
        var singleChessBoardController = window.singleChessBoardController;
        if (chessBoardController) {
            // 测试联机版棋盘
            this.updateStatus("触发联机版格子消失动画");
            // 随机选择一个格子进行动画测试
            var x = Math.floor(Math.random() * 8);
            var y = Math.floor(Math.random() * 8);
            chessBoardController.removeGridAt(x, y, false);
            this.updateStatus("\u8054\u673A\u7248\u683C\u5B50(" + x + ", " + y + ")\u6D88\u5931\u52A8\u753B\u5DF2\u89E6\u53D1");
        }
        else if (singleChessBoardController) {
            // 测试单人版棋盘
            this.updateStatus("触发单人版格子消失动画");
            // 随机选择一个格子进行动画测试
            var x = Math.floor(Math.random() * 8);
            var y = Math.floor(Math.random() * 8);
            singleChessBoardController.removeGridAt(x, y, false);
            this.updateStatus("\u5355\u4EBA\u7248\u683C\u5B50(" + x + ", " + y + ")\u6D88\u5931\u52A8\u753B\u5DF2\u89E6\u53D1");
        }
        else {
            this.updateStatus("未找到棋盘控制器实例");
        }
    };
    /**
     * 测试六边形格子消失动画
     */
    GridAnimationTest.prototype.testHexGridAnimation = function () {
        this.updateStatus("测试六边形格子消失动画...");
        // 获取六边形棋盘控制器实例
        var hexChessBoardController = window.hexChessBoardController;
        if (hexChessBoardController) {
            this.updateStatus("触发六边形格子消失动画");
            // 随机选择一个六边形格子进行动画测试
            var q = Math.floor(Math.random() * 5) - 2; // -2 到 2
            var r = Math.floor(Math.random() * 5) - 2; // -2 到 2
            hexChessBoardController.hideHexGridAt(q, r, false);
            this.updateStatus("\u516D\u8FB9\u5F62\u683C\u5B50(" + q + ", " + r + ")\u6D88\u5931\u52A8\u753B\u5DF2\u89E6\u53D1");
        }
        else {
            this.updateStatus("未找到六边形棋盘控制器实例");
        }
    };
    /**
     * 重置所有格子
     */
    GridAnimationTest.prototype.resetAllGrids = function () {
        this.updateStatus("重置所有格子...");
        // 获取所有棋盘控制器实例
        var chessBoardController = window.chessBoardController;
        var singleChessBoardController = window.singleChessBoardController;
        var hexChessBoardController = window.hexChessBoardController;
        var resetCount = 0;
        if (chessBoardController && chessBoardController.resetGameScene) {
            chessBoardController.resetGameScene();
            resetCount++;
        }
        if (singleChessBoardController && singleChessBoardController.resetBoard) {
            singleChessBoardController.resetBoard();
            resetCount++;
        }
        if (hexChessBoardController && hexChessBoardController.resetGameScene) {
            hexChessBoardController.resetGameScene();
            resetCount++;
        }
        if (resetCount > 0) {
            this.updateStatus("\u5DF2\u91CD\u7F6E " + resetCount + " \u4E2A\u68CB\u76D8\u7684\u683C\u5B50");
        }
        else {
            this.updateStatus("未找到可重置的棋盘控制器");
        }
    };
    /**
     * 更新状态显示
     */
    GridAnimationTest.prototype.updateStatus = function (message) {
        if (this.statusLabel) {
            this.statusLabel.string = message;
        }
        console.log("[GridAnimationTest] " + message);
    };
    __decorate([
        property(cc.Button)
    ], GridAnimationTest.prototype, "testSquareAnimationBtn", void 0);
    __decorate([
        property(cc.Button)
    ], GridAnimationTest.prototype, "testHexAnimationBtn", void 0);
    __decorate([
        property(cc.Button)
    ], GridAnimationTest.prototype, "resetGridsBtn", void 0);
    __decorate([
        property(cc.Label)
    ], GridAnimationTest.prototype, "statusLabel", void 0);
    GridAnimationTest = __decorate([
        ccclass
    ], GridAnimationTest);
    return GridAnimationTest;
}(cc.Component));
exports.default = GridAnimationTest;

cc._RF.pop();