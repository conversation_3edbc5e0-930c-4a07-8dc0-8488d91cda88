{"version": 3, "sources": ["assets/scripts/test/GridAnimationTest.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAAM,IAAA,KAAwB,EAAE,CAAC,UAAU,EAAnC,OAAO,aAAA,EAAE,QAAQ,cAAkB,CAAC;AAG5C;IAA+C,qCAAY;IAA3D;QAAA,qEA6HC;QA1HG,4BAAsB,GAAc,IAAI,CAAC;QAGzC,yBAAmB,GAAc,IAAI,CAAC;QAGtC,mBAAa,GAAc,IAAI,CAAC;QAGhC,iBAAW,GAAa,IAAI,CAAC;;IAiHjC,CAAC;IA/GG,kCAAM,GAAN;QACI,WAAW;QACX,IAAI,IAAI,CAAC,sBAAsB,EAAE;YAC7B,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC,uBAAuB,EAAE,IAAI,CAAC,CAAC;SACpF;QAED,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC1B,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC,oBAAoB,EAAE,IAAI,CAAC,CAAC;SAC9E;QAED,IAAI,IAAI,CAAC,aAAa,EAAE;YACpB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;SACjE;IACL,CAAC;IAED;;OAEG;IACH,mDAAuB,GAAvB;QACI,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC,CAAC;QAEpC,YAAY;QACZ,IAAM,oBAAoB,GAAI,MAAc,CAAC,oBAAoB,CAAC;QAClE,IAAM,0BAA0B,GAAI,MAAc,CAAC,0BAA0B,CAAC;QAE9E,IAAI,oBAAoB,EAAE;YACtB,UAAU;YACV,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC;YACjC,iBAAiB;YACjB,IAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC;YACxC,IAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC;YACxC,oBAAoB,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;YAC/C,IAAI,CAAC,YAAY,CAAC,oCAAS,CAAC,UAAK,CAAC,gDAAU,CAAC,CAAC;SACjD;aAAM,IAAI,0BAA0B,EAAE;YACnC,UAAU;YACV,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC;YACjC,iBAAiB;YACjB,IAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC;YACxC,IAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC;YACxC,0BAA0B,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;YACrD,IAAI,CAAC,YAAY,CAAC,oCAAS,CAAC,UAAK,CAAC,gDAAU,CAAC,CAAC;SACjD;aAAM;YACH,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;SACnC;IACL,CAAC;IAED;;OAEG;IACH,gDAAoB,GAApB;QACI,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC,CAAC;QAEpC,eAAe;QACf,IAAM,uBAAuB,GAAI,MAAc,CAAC,uBAAuB,CAAC;QAExE,IAAI,uBAAuB,EAAE;YACzB,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC;YACjC,oBAAoB;YACpB,IAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS;YACtD,IAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS;YACtD,uBAAuB,CAAC,aAAa,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;YACnD,IAAI,CAAC,YAAY,CAAC,oCAAS,CAAC,UAAK,CAAC,gDAAU,CAAC,CAAC;SACjD;aAAM;YACH,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,CAAC;SACtC;IACL,CAAC;IAED;;OAEG;IACH,yCAAa,GAAb;QACI,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;QAE/B,cAAc;QACd,IAAM,oBAAoB,GAAI,MAAc,CAAC,oBAAoB,CAAC;QAClE,IAAM,0BAA0B,GAAI,MAAc,CAAC,0BAA0B,CAAC;QAC9E,IAAM,uBAAuB,GAAI,MAAc,CAAC,uBAAuB,CAAC;QAExE,IAAI,UAAU,GAAG,CAAC,CAAC;QAEnB,IAAI,oBAAoB,IAAI,oBAAoB,CAAC,cAAc,EAAE;YAC7D,oBAAoB,CAAC,cAAc,EAAE,CAAC;YACtC,UAAU,EAAE,CAAC;SAChB;QAED,IAAI,0BAA0B,IAAI,0BAA0B,CAAC,UAAU,EAAE;YACrE,0BAA0B,CAAC,UAAU,EAAE,CAAC;YACxC,UAAU,EAAE,CAAC;SAChB;QAED,IAAI,uBAAuB,IAAI,uBAAuB,CAAC,cAAc,EAAE;YACnE,uBAAuB,CAAC,cAAc,EAAE,CAAC;YACzC,UAAU,EAAE,CAAC;SAChB;QAED,IAAI,UAAU,GAAG,CAAC,EAAE;YAChB,IAAI,CAAC,YAAY,CAAC,wBAAO,UAAU,0CAAS,CAAC,CAAC;SACjD;aAAM;YACH,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,CAAC;SACrC;IACL,CAAC;IAED;;OAEG;IACK,wCAAY,GAApB,UAAqB,OAAe;QAChC,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,OAAO,CAAC;SACrC;QACD,OAAO,CAAC,GAAG,CAAC,yBAAuB,OAAS,CAAC,CAAC;IAClD,CAAC;IAzHD;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;qEACqB;IAGzC;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;kEACkB;IAGtC;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;4DACY;IAGhC;QADC,QAAQ,CAAC,EAAE,CAAC,KAAK,CAAC;0DACU;IAZZ,iBAAiB;QADrC,OAAO;OACa,iBAAiB,CA6HrC;IAAD,wBAAC;CA7HD,AA6HC,CA7H8C,EAAE,CAAC,SAAS,GA6H1D;kBA7HoB,iBAAiB", "file": "", "sourceRoot": "/", "sourcesContent": ["const { ccclass, property } = cc._decorator;\n\n@ccclass\nexport default class GridAnimationTest extends cc.Component {\n\n    @property(cc.Button)\n    testSquareAnimationBtn: cc.Button = null;\n\n    @property(cc.Button)\n    testHexAnimationBtn: cc.Button = null;\n\n    @property(cc.Button)\n    resetGridsBtn: cc.Button = null;\n\n    @property(cc.Label)\n    statusLabel: cc.Label = null;\n\n    onLoad() {\n        // 设置按钮点击事件\n        if (this.testSquareAnimationBtn) {\n            this.testSquareAnimationBtn.node.on('click', this.testSquareGridAnimation, this);\n        }\n\n        if (this.testHexAnimationBtn) {\n            this.testHexAnimationBtn.node.on('click', this.testHexGridAnimation, this);\n        }\n\n        if (this.resetGridsBtn) {\n            this.resetGridsBtn.node.on('click', this.resetAllGrids, this);\n        }\n    }\n\n    /**\n     * 测试四边形格子消失动画\n     */\n    testSquareGridAnimation() {\n        this.updateStatus(\"测试四边形格子消失动画...\");\n        \n        // 获取棋盘控制器实例\n        const chessBoardController = (window as any).chessBoardController;\n        const singleChessBoardController = (window as any).singleChessBoardController;\n        \n        if (chessBoardController) {\n            // 测试联机版棋盘\n            this.updateStatus(\"触发联机版格子消失动画\");\n            // 随机选择一个格子进行动画测试\n            const x = Math.floor(Math.random() * 8);\n            const y = Math.floor(Math.random() * 8);\n            chessBoardController.removeGridAt(x, y, false);\n            this.updateStatus(`联机版格子(${x}, ${y})消失动画已触发`);\n        } else if (singleChessBoardController) {\n            // 测试单人版棋盘\n            this.updateStatus(\"触发单人版格子消失动画\");\n            // 随机选择一个格子进行动画测试\n            const x = Math.floor(Math.random() * 8);\n            const y = Math.floor(Math.random() * 8);\n            singleChessBoardController.removeGridAt(x, y, false);\n            this.updateStatus(`单人版格子(${x}, ${y})消失动画已触发`);\n        } else {\n            this.updateStatus(\"未找到棋盘控制器实例\");\n        }\n    }\n\n    /**\n     * 测试六边形格子消失动画\n     */\n    testHexGridAnimation() {\n        this.updateStatus(\"测试六边形格子消失动画...\");\n        \n        // 获取六边形棋盘控制器实例\n        const hexChessBoardController = (window as any).hexChessBoardController;\n        \n        if (hexChessBoardController) {\n            this.updateStatus(\"触发六边形格子消失动画\");\n            // 随机选择一个六边形格子进行动画测试\n            const q = Math.floor(Math.random() * 5) - 2; // -2 到 2\n            const r = Math.floor(Math.random() * 5) - 2; // -2 到 2\n            hexChessBoardController.hideHexGridAt(q, r, false);\n            this.updateStatus(`六边形格子(${q}, ${r})消失动画已触发`);\n        } else {\n            this.updateStatus(\"未找到六边形棋盘控制器实例\");\n        }\n    }\n\n    /**\n     * 重置所有格子\n     */\n    resetAllGrids() {\n        this.updateStatus(\"重置所有格子...\");\n        \n        // 获取所有棋盘控制器实例\n        const chessBoardController = (window as any).chessBoardController;\n        const singleChessBoardController = (window as any).singleChessBoardController;\n        const hexChessBoardController = (window as any).hexChessBoardController;\n        \n        let resetCount = 0;\n        \n        if (chessBoardController && chessBoardController.resetGameScene) {\n            chessBoardController.resetGameScene();\n            resetCount++;\n        }\n        \n        if (singleChessBoardController && singleChessBoardController.resetBoard) {\n            singleChessBoardController.resetBoard();\n            resetCount++;\n        }\n        \n        if (hexChessBoardController && hexChessBoardController.resetGameScene) {\n            hexChessBoardController.resetGameScene();\n            resetCount++;\n        }\n        \n        if (resetCount > 0) {\n            this.updateStatus(`已重置 ${resetCount} 个棋盘的格子`);\n        } else {\n            this.updateStatus(\"未找到可重置的棋盘控制器\");\n        }\n    }\n\n    /**\n     * 更新状态显示\n     */\n    private updateStatus(message: string) {\n        if (this.statusLabel) {\n            this.statusLabel.string = message;\n        }\n        console.log(`[GridAnimationTest] ${message}`);\n    }\n}\n"]}