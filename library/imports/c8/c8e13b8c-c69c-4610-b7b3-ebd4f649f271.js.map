{"version": 3, "sources": ["assets/scripts/level/LevelPageController.ts"], "names": [], "mappings": ";;;;;AAAA,oBAAoB;AACpB,4EAA4E;AAC5E,mBAAmB;AACnB,sFAAsF;AACtF,8BAA8B;AAC9B,sFAAsF;;;;;;;;;;;;;;;;;;;;;AAGtF,uEAAkE;AAClE,uCAAsC;AACtC,yCAAwC;AACxC,sEAA+E;AAC/E,uFAAkF;AAClF,4DAA2D;AAC3D,8CAA6C;AAC7C,6CAA4C;AAC5C,qDAAkD;AAG5C,IAAA,KAAwB,EAAE,CAAC,UAAU,EAAnC,OAAO,aAAA,EAAE,QAAQ,cAAkB,CAAC;AAG5C;IAAiD,uCAAY;IAA7D;QAAA,qEAg+BC;QA99BG,OAAO;QAEP,gBAAU,GAAY,IAAI,CAAC;QAE3B,SAAS;QAET,qBAAe,GAAc,IAAI,CAAC;QAElC,UAAU;QAEV,oBAAc,GAAa,IAAI,CAAC;QAEhC,YAAY;QAEZ,uBAAiB,GAAa,IAAI,CAAC;QAEnC,SAAS;QAET,2BAAqB,GAA0B,IAAI,CAAC;QAEpD,eAAe;QAEf,mBAAa,GAAY,IAAI,CAAC;QAE9B,eAAe;QAEf,kBAAY,GAAY,IAAI,CAAC;QAE7B,eAAe;QAEf,kBAAY,GAAY,IAAI,CAAC;QAE7B,WAAW;QAEX,kBAAY,GAAY,IAAI,CAAC,CAAC,0CAA0C;QAGxE,kBAAY,GAAY,IAAI,CAAC,CAAC,0CAA0C;QAGxE,kBAAY,GAAY,IAAI,CAAC,CAAC,0CAA0C;QAGxE,mBAAa,GAAY,IAAI,CAAC,CAAC,2CAA2C;QAG1E,oBAAc,GAAY,IAAI,CAAC,CAAC,4CAA4C;QAE5E,WAAW;QAEX,mBAAa,GAAY,IAAI,CAAC,CAAC,2CAA2C;QAG1E,mBAAa,GAAY,IAAI,CAAC,CAAC,2CAA2C;QAG1E,mBAAa,GAAY,IAAI,CAAC,CAAC,2CAA2C;QAG1E,mBAAa,GAAY,IAAI,CAAC,CAAC,2CAA2C;QAG1E,mBAAa,GAAY,IAAI,CAAC,CAAC,2CAA2C;QAG1E,mBAAa,GAAY,IAAI,CAAC,CAAC,2CAA2C;QAE1E,YAAY;QAEZ,gCAA0B,GAA+B,IAAI,CAAC;QAE9D,mBAAmB;QAEnB,0BAAoB,GAAc,IAAI,CAAC;QAEvC,kBAAkB;QAElB,qBAAe,GAAc,IAAI,CAAC;QAElC,oBAAoB;QACZ,oBAAc,GAAc,EAAE,CAAC;QAEvC,WAAW;QAEX,yBAAmB,GAAY,IAAI,CAAC,CAAC,qBAAqB;QAG1D,iBAAW,GAAY,IAAI,CAAC,CAAC,8BAA8B;QAG3D,gBAAU,GAAY,IAAI,CAAC,CAAC,sCAAsC;QAGlE,eAAS,GAAY,IAAI,CAAC,CAAC,qCAAqC;QAGhE,iBAAW,GAAc,IAAI,CAAC,CAAC,SAAS;QAGxC,qBAAe,GAAc,IAAI,CAAC,CAAC,QAAQ;QAG3C,gBAAU,GAAc,IAAI,CAAC,CAAC,OAAO;QAErC,SAAS;QACD,kBAAY,GAAW,CAAC,CAAC;QACzB,sBAAgB,GAA4B,IAAI,CAAC;QACjD,mBAAa,GAAW,CAAC,CAAC,CAAC,cAAc;QACzC,6BAAuB,GAA+B,IAAI,CAAC,CAAC,YAAY;QAEhF,gBAAgB;QACR,sBAAgB,GAAY,KAAK,CAAC;QAE1C,SAAS;QACD,sBAAgB,GAAY,IAAI,CAAC,CAAC,cAAc;QAChD,gBAAU,GAAY,KAAK,CAAC,CAAC,SAAS;;IA22BlD,CAAC;IAz2BG,oCAAM,GAAN;QAAA,iBAoBC;QAnBG,0CAA0C;QAC1C,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,aAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,UAAU,EAAE,eAAM,CAAC,SAAS,GAAG,sBAAsB,EAAE,eAAM,CAAC,SAAS,GAAG,uBAAuB,EAAE;gBAC3H,KAAI,CAAC,iBAAiB,EAAE,CAAC;YAC7B,CAAC,CAAC,CAAC;SACN;QAED,eAAe;QACf,IAAI,IAAI,CAAC,eAAe,EAAE;YACtB,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC,sBAAsB,EAAE,IAAI,CAAC,CAAC;SAC5E;QAED,aAAa;QACb,IAAI,CAAC,iCAAiC,EAAE,CAAC;QAEzC,aAAa;QACb,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAE9B,iDAAiD;IACrD,CAAC;IAED,mCAAK,GAAL;QACI,eAAe;QACf,IAAI,CAAC,eAAe,EAAE,CAAC;QAEvB,aAAa;QACb,IAAI,CAAC,gBAAgB,EAAE,CAAC;IAC5B,CAAC;IAED;;OAEG;IACK,+CAAiB,GAAzB;QACI,mCAAmC;QACnC,IAAI,IAAI,CAAC,aAAa,IAAI,CAAC,EAAE;YAEzB,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC3B,OAAO;SACV;QAED,oCAAoC;QACpC,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAE5B,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC,EAAE;YAEnC,CAAC,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;SAC1B;aAAM;YACH,EAAE,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;SACxC;IACL,CAAC;IAED;;OAEG;IACK,iDAAmB,GAA3B;QACI,oCAAoC;QACpC,IAAM,iBAAiB,GAAG,EAAE,CAAC,IAAI,CAAC,oBAAoB,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAClF,IAAI,iBAAiB,EAAE;YACnB,IAAM,aAAa,GAAG,iBAAiB,CAAC,YAAY,CAAC,iCAAuB,CAAC,CAAC;YAC9E,IAAI,aAAa,EAAE;gBACf,aAAa,CAAC,cAAc,CAAC,kCAAQ,CAAC,SAAS,CAAC,CAAC;aACpD;SACJ;IACL,CAAC;IAED;;OAEG;IACK,oDAAsB,GAA9B;QACI,kDAAkD;QAClD,kBAAkB;IAEtB,CAAC;IAED;;;OAGG;IACI,+CAAiB,GAAxB,UAAyB,SAAkC;QAEvD,IAAI,CAAC,gBAAgB,GAAG,SAAS,CAAC;QAElC,iBAAiB;QACjB,IAAI,SAAS,CAAC,MAAM,EAAE;YAClB,IAAI,CAAC,aAAa,GAAG,SAAS,CAAC,MAAM,CAAC;SACzC;QAED,oBAAoB;QACpB,IAAI,CAAC,eAAe,EAAE,CAAC;QAEvB,WAAW;QACX,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;QAE9B,2BAA2B;QAC3B,IAAI,IAAI,CAAC,uBAAuB,EAAE;YAE9B,IAAI,CAAC,uBAAuB,CAAC,UAAU,EAAE,CAAC;SAC7C;QAED,UAAU;QACV,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;QAE5C,8BAA8B;QAC9B,4BAA4B;QAE5B,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QAEnC,kCAAkC;QAClC,IAAI,IAAI,CAAC,uBAAuB,EAAE;YAC9B,IAAI,CAAC,uBAAuB,CAAC,iBAAiB,EAAE,CAAC;SACpD;IACL,CAAC;IAED;;;OAGG;IACK,+CAAiB,GAAzB,UAA0B,SAAiB;QACvC,IAAI,IAAI,CAAC,cAAc,EAAE;YACrB,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,SAAS,CAAC,QAAQ,EAAE,CAAC;SAErD;IACL,CAAC;IAED;;;OAGG;IACK,kDAAoB,GAA5B,UAA6B,WAAmB;QAC5C,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACxB,IAAI,CAAC,iBAAiB,CAAC,MAAM,GAAG,WAAI,WAAW,WAAG,CAAC;SAEtD;IACL,CAAC;IAED;;;OAGG;IACK,wCAAU,GAAlB,UAAmB,WAAmB;QAClC,SAAS;QACT,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,OAAO;SACV;QACD,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;QAEvB,YAAY;QACZ,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC,CAAC;QAEvC,cAAc;QACd,IAAM,aAAa,GAAG,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAC;QAC1D,IAAI,CAAC,aAAa,EAAE;YAChB,EAAE,CAAC,IAAI,CAAC,iDAAY,WAAa,CAAC,CAAC;YACnC,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;YACxB,OAAO;SACV;QAED,0BAA0B;QAC1B,IAAI,IAAI,CAAC,gBAAgB,KAAK,aAAa,CAAC,OAAO,EAAE;YACjD,eAAe;YACf,IAAI,IAAI,CAAC,gBAAgB,EAAE;gBACvB,IAAI,CAAC,gBAAgB,CAAC,MAAM,GAAG,KAAK,CAAC;aACxC;YAED,cAAc;YACd,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;YACnD,IAAI,CAAC,oBAAoB,CAAC,aAAa,CAAC,OAAO,EAAE,aAAa,CAAC,OAAO,CAAC,CAAC;YAExE,YAAY;YACZ,IAAI,CAAC,gBAAgB,GAAG,aAAa,CAAC,OAAO,CAAC;SACjD;QAED,iBAAiB;QACjB,IAAI,IAAI,CAAC,0BAA0B,EAAE;YACjC,aAAa;YACb,IAAM,SAAS,GAAG,IAAI,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAC;YACxD,IAAI,CAAC,0BAA0B,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;YACrD,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,0BAA0B,CAAC;SAClE;aAAM;YACH,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC;SACvC;QAED,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;IAC5B,CAAC;IAED;;;OAGG;IACK,+CAAiB,GAAzB,UAA0B,WAAmB;QACzC,IAAI,WAAW,IAAI,CAAC,IAAI,WAAW,IAAI,CAAC,EAAE;YACtC,OAAO,EAAC,OAAO,EAAE,IAAI,CAAC,YAAY,EAAE,OAAO,EAAE,UAAU,EAAE,aAAa,EAAE,MAAM,EAAC,CAAC;SACnF;aAAM,IAAI,WAAW,KAAK,CAAC,EAAE;YAC1B,OAAO,EAAC,OAAO,EAAE,IAAI,CAAC,aAAa,EAAE,OAAO,EAAE,YAAY,EAAE,aAAa,EAAE,MAAM,EAAC,CAAC;SACtF;aAAM,IAAI,WAAW,IAAI,CAAC,IAAI,WAAW,IAAI,CAAC,EAAE;YAC7C,OAAO,EAAC,OAAO,EAAE,IAAI,CAAC,YAAY,EAAE,OAAO,EAAE,UAAU,EAAE,aAAa,EAAE,MAAM,EAAC,CAAC;SACnF;aAAM,IAAI,WAAW,KAAK,EAAE,EAAE;YAC3B,OAAO,EAAC,OAAO,EAAE,IAAI,CAAC,aAAa,EAAE,OAAO,EAAE,YAAY,EAAE,aAAa,EAAE,MAAM,EAAC,CAAC;SACtF;aAAM,IAAI,WAAW,IAAI,EAAE,IAAI,WAAW,IAAI,EAAE,EAAE;YAC/C,OAAO,EAAC,OAAO,EAAE,IAAI,CAAC,YAAY,EAAE,OAAO,EAAE,UAAU,EAAE,aAAa,EAAE,MAAM,EAAC,CAAC;SACnF;aAAM,IAAI,WAAW,KAAK,EAAE,EAAE;YAC3B,OAAO,EAAC,OAAO,EAAE,IAAI,CAAC,aAAa,EAAE,OAAO,EAAE,YAAY,EAAE,aAAa,EAAE,MAAM,EAAC,CAAC;SACtF;aAAM,IAAI,WAAW,IAAI,EAAE,IAAI,WAAW,IAAI,EAAE,EAAE;YAC/C,OAAO,EAAC,OAAO,EAAE,IAAI,CAAC,aAAa,EAAE,OAAO,EAAE,WAAW,EAAE,aAAa,EAAE,MAAM,EAAC,CAAC;SACrF;aAAM,IAAI,WAAW,KAAK,EAAE,EAAE;YAC3B,OAAO,EAAC,OAAO,EAAE,IAAI,CAAC,aAAa,EAAE,OAAO,EAAE,YAAY,EAAE,aAAa,EAAE,MAAM,EAAC,CAAC;SACtF;aAAM,IAAI,WAAW,IAAI,EAAE,IAAI,WAAW,IAAI,EAAE,EAAE;YAC/C,OAAO,EAAC,OAAO,EAAE,IAAI,CAAC,cAAc,EAAE,OAAO,EAAE,YAAY,EAAE,aAAa,EAAE,MAAM,EAAC,CAAC;SACvF;aAAM,IAAI,WAAW,KAAK,EAAE,EAAE;YAC3B,OAAO,EAAC,OAAO,EAAE,IAAI,CAAC,aAAa,EAAE,OAAO,EAAE,YAAY,EAAE,aAAa,EAAE,MAAM,EAAC,CAAC;SACtF;aAAM,IAAI,WAAW,IAAI,EAAE,IAAI,WAAW,IAAI,EAAE,EAAE;YAC/C,OAAO,EAAC,OAAO,EAAE,IAAI,CAAC,cAAc,EAAE,OAAO,EAAE,YAAY,EAAE,aAAa,EAAE,MAAM,EAAC,CAAC;SACvF;aAAM,IAAI,WAAW,KAAK,EAAE,EAAE;YAC3B,OAAO,EAAC,OAAO,EAAE,IAAI,CAAC,aAAa,EAAE,OAAO,EAAE,YAAY,EAAE,aAAa,EAAE,MAAM,EAAC,CAAC;SACtF;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAID;;;OAGG;IACK,iDAAmB,GAA3B,UAA4B,WAAmB;QAC3C,IAAI,WAAW,IAAI,CAAC,IAAI,WAAW,IAAI,CAAC,EAAE;YACtC,OAAO,KAAK,CAAC;SAChB;aAAM,IAAI,WAAW,IAAI,CAAC,IAAI,WAAW,IAAI,CAAC,EAAE;YAC7C,OAAO,KAAK,CAAC;SAChB;aAAM,IAAI,WAAW,IAAI,EAAE,IAAI,WAAW,IAAI,EAAE,EAAE;YAC/C,OAAO,KAAK,CAAC;SAChB;aAAM,IAAI,WAAW,IAAI,EAAE,IAAI,WAAW,IAAI,EAAE,EAAE;YAC/C,OAAO,MAAM,CAAC;SACjB;aAAM,IAAI,WAAW,IAAI,EAAE,IAAI,WAAW,IAAI,EAAE,IAAI,WAAW,IAAI,EAAE,IAAI,WAAW,IAAI,EAAE,EAAE;YACzF,OAAO,OAAO,CAAC;SAClB;QACD,OAAO,KAAK,CAAC,CAAC,KAAK;IACvB,CAAC;IAED;;;;OAIG;IACK,kDAAoB,GAA5B,UAA6B,OAAgB,EAAE,OAAe;QAC1D,IAAI,OAAO,EAAE;YACT,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC;SACzB;aAAM;YACH,EAAE,CAAC,IAAI,CAAC,wDAAc,OAAS,CAAC,CAAC;SACpC;IACL,CAAC;IAED;;;OAGG;IACK,8CAAgB,GAAxB,UAAyB,aAA8B;QACnD,uBAAuB;QACvB,IAAI,IAAI,CAAC,aAAa,EAAE;YACpB,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,IAAI,CAAC;SACpC;QAED,qBAAqB;QACrB,IAAI,aAAa,KAAK,MAAM,EAAE;YAC1B,IAAI,IAAI,CAAC,YAAY,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE;gBAChD,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,IAAI,CAAC;aACnC;YACD,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE;gBAC/C,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,KAAK,CAAC;aACpC;SACJ;aAAM;YACH,IAAI,IAAI,CAAC,YAAY,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE;gBAChD,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,IAAI,CAAC;aACnC;YACD,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE;gBAC/C,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,KAAK,CAAC;aACpC;SACJ;IACL,CAAC;IAED;;OAEG;IACK,6CAAe,GAAvB;QACI,IAAM,WAAW,GAAG;YAChB,IAAI,CAAC,YAAY;YACjB,IAAI,CAAC,YAAY;YACjB,IAAI,CAAC,YAAY;YACjB,IAAI,CAAC,aAAa;YAClB,IAAI,CAAC,cAAc;YACnB,IAAI,CAAC,aAAa;YAClB,IAAI,CAAC,aAAa;YAClB,IAAI,CAAC,aAAa;YAClB,IAAI,CAAC,aAAa;YAClB,IAAI,CAAC,aAAa;YAClB,IAAI,CAAC,aAAa;SACrB,CAAC;QAEF,WAAW,CAAC,OAAO,CAAC,UAAA,IAAI;YACpB,IAAI,IAAI,EAAE;gBACN,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;aACvB;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;OAGG;IACI,6CAAe,GAAtB,UAAuB,WAAmB;QAGtC,oBAAoB;QACpB,IAAI,CAAC,eAAe,EAAE,CAAC;QAEvB,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC;QAEhC,gBAAgB;QAChB,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC;IACjC,CAAC;IAED;;OAEG;IACI,6CAAe,GAAtB;QACI,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED;;OAEG;IACI,iDAAmB,GAA1B;QACI,OAAO,IAAI,CAAC,gBAAgB,CAAC;IACjC,CAAC;IAED;;OAEG;IACI,8CAAgB,GAAvB;QACI,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAED;;OAEG;IACK,kDAAoB,GAA5B;QACI,uBAAuB;QACvB,IAAI,IAAI,CAAC,aAAa,EAAE;YACpB,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,IAAI,CAAC;SAEpC;QAED,cAAc;QACd,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,KAAK,CAAC;SAEpC;QACD,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,KAAK,CAAC;SAEpC;QAED,gBAAgB;QAChB,IAAI,CAAC,eAAe,EAAE,CAAC;IAC3B,CAAC;IAED;;OAEG;IACK,0CAAY,GAApB;QACI,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,IAAI,CAAC;SAEnC;aAAM;YACH,EAAE,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;SACjC;IACL,CAAC;IAED;;OAEG;IACK,0CAAY,GAApB;QACI,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,IAAI,CAAC;SAEnC;aAAM;YACH,EAAE,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;SACjC;IACL,CAAC;IAED;;OAEG;IACI,wDAA0B,GAAjC;QACI,OAAO,IAAI,CAAC,uBAAuB,CAAC;IACxC,CAAC;IAED;;;OAGG;IACI,2DAA6B,GAApC,UAAqC,QAAa;QAC9C,IAAI,IAAI,CAAC,uBAAuB,EAAE;YACtB,IAAA,CAAC,GAA+B,QAAQ,EAAvC,EAAE,CAAC,GAA4B,QAAQ,EAApC,EAAE,MAAM,GAAoB,QAAQ,OAA5B,EAAE,aAAa,GAAK,QAAQ,cAAb,CAAc;YAEjD,SAAS;YACT,IAAI,CAAC,KAAK,SAAS,IAAI,CAAC,KAAK,SAAS,IAAI,MAAM,KAAK,SAAS,EAAE;gBAC5D,IAAI,CAAC,uBAAuB,CAAC,mBAAmB,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;aAClE;YAED,SAAS;YACT,IAAI,aAAa,IAAI,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE;gBAC/C,IAAI,CAAC,uBAAuB,CAAC,mBAAmB,CAAC,aAAa,CAAC,CAAC;aACnE;SACJ;IACL,CAAC;IAED;;;OAGG;IACI,qDAAuB,GAA9B,UAA+B,WAAgB;QAC3C,IAAI,IAAI,CAAC,uBAAuB,EAAE;YAC9B,SAAS;YACT,IAAI,CAAC,uBAAuB,CAAC,mBAAmB,EAAE,CAAC;YAEnD,SAAS;YACT,IAAI,CAAC,uBAAuB,CAAC,cAAc,EAAE,CAAC;SACjD;IACL,CAAC;IAED;;OAEG;IACI,0DAA4B,GAAnC;QAEI,IAAI,IAAI,CAAC,uBAAuB,EAAE;YAC9B,uBAAuB;YACvB,IAAI,CAAC,uBAAuB,CAAC,UAAU,EAAE,CAAC;YAE1C,WAAW;YACX,IAAI,CAAC,uBAAuB,CAAC,kBAAkB,EAAE,CAAC;SACrD;IACL,CAAC;IAED;;OAEG;IACK,+DAAiC,GAAzC;QACI,gBAAgB;QAChB,iBAAO,CAAC,KAAK,CAAC,gBAAgB,CAAC,uBAAS,CAAC,cAAc,EAAE,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAC;IAC1F,CAAC;IAED;;OAEG;IACK,iEAAmC,GAA3C;QACI,iBAAO,CAAC,KAAK,CAAC,mBAAmB,CAAC,uBAAS,CAAC,cAAc,EAAE,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAC;IAC7F,CAAC;IAED;;;OAGG;IACK,8CAAgB,GAAxB,UAAyB,WAAgC;QACrD,QAAQ,WAAW,CAAC,KAAK,EAAE;YACvB,KAAK,qBAAS,CAAC,sBAAsB;gBACjC,IAAI,CAAC,yBAAyB,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;gBACjD,MAAM;YACV,KAAK,qBAAS,CAAC,mBAAmB;gBAC9B,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;gBACtC,MAAM;SACb;IACL,CAAC;IAED;;;OAGG;IACI,uDAAyB,GAAhC,UAAiC,QAAa;QAG1C,IAAI,IAAI,CAAC,uBAAuB,EAAE;YAC9B,oBAAoB;YAEhB,IAAA,CAAC,GAGD,QAAQ,EAHP,EAAE,CAAC,GAGJ,QAAQ,EAHJ,EAAE,MAAM,GAGZ,QAAQ,OAHI,EAAE,MAAM,GAGpB,QAAQ,OAHY,EACpB,aAAa,GAEb,QAAQ,cAFK,EAAE,aAAa,GAE5B,QAAQ,cAFoB,EAAE,SAAS,GAEvC,QAAQ,UAF+B,EAAE,cAAc,GAEvD,QAAQ,eAF+C,EACvD,gBAAgB,GAChB,QAAQ,iBADQ,CAAE,aAAa;YAAf,CACP;YAEb,oBAAoB;YACpB,IAAI,CAAC,KAAK,SAAS,IAAI,CAAC,KAAK,SAAS,IAAI,MAAM,KAAK,SAAS,EAAE;gBAE5D,IAAI,MAAM,KAAK,CAAC,EAAE;oBACd,0CAA0C;oBAE1C,wCAAwC;iBAC3C;qBAAM,IAAI,MAAM,KAAK,CAAC,EAAE;oBACrB,OAAO;oBACP,OAAO,CAAC,GAAG,CAAC,6CAAa,CAAC,UAAK,CAAC,yBAAU,MAAQ,CAAC,CAAC;oBAEpD,WAAW;oBACX,IAAI,MAAM,KAAK,MAAM,IAAI,MAAM,KAAK,MAAM,EAAE;wBACxC,OAAO,CAAC,GAAG,CAAC,uCAAuC,CAAC,CAAC;wBACrD,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;qBAChC;oBAED,IAAI,CAAC,uBAAuB,CAAC,mBAAmB,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;oBAE/D,WAAW;oBACX,IAAI,gBAAgB,IAAI,KAAK,CAAC,OAAO,CAAC,gBAAgB,CAAC,IAAI,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE;wBAEpF,IAAI,CAAC,uBAAuB,CAAC,sBAAsB,CAAC,gBAAgB,CAAC,CAAC;qBACzE;iBACJ;qBAAM;oBACH,eAAe;oBAEf,IAAI,CAAC,uBAAuB,CAAC,mBAAmB,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;iBAClE;aACJ;SAOJ;IACL,CAAC;IAED;;;OAGG;IACI,4CAAc,GAArB,UAAsB,WAAgB;QAAtC,iBAuCC;QApCG,IAAI,IAAI,CAAC,uBAAuB,EAAE;YAC9B,SAAS;YACT,IAAI,CAAC,uBAAuB,CAAC,mBAAmB,EAAE,CAAC;YAEnD,gBAAgB;YAChB,IAAI,CAAC,uBAAuB,CAAC,cAAc,EAAE,CAAC;SACjD;QAED,mCAAmC;QACnC,IAAM,YAAY,GAAG,CAAC,WAAW,CAAC,OAAO,CAAC;QAC1C,IAAM,eAAe,GAAG,IAAI,CAAC,gBAAgB;YACtB,CAAC,IAAI,CAAC,uBAAuB,IAAI,IAAI,CAAC,uBAAuB,CAAC,yBAAyB,EAAE,CAAC,CAAC;QAElH,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;QAChC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,WAAW,CAAC,CAAC;QACzC,OAAO,CAAC,GAAG,CAAC,eAAe,EAAE,YAAY,CAAC,CAAC;QAC3C,OAAO,CAAC,GAAG,CAAC,mBAAmB,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;QACxD,OAAO,CAAC,GAAG,CAAC,0BAA0B,EAAE,CAAC,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;QACxE,OAAO,CAAC,GAAG,CAAC,kBAAkB,EAAE,eAAe,CAAC,CAAC;QAEjD,IAAI,YAAY,IAAI,eAAe,EAAE;YACjC,2BAA2B;YAC3B,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAC;YAChD,IAAI,CAAC,YAAY,CAAC;gBACd,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;gBAChC,KAAI,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAC;gBACtC,OAAO;gBACP,KAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;YAClC,CAAC,EAAE,GAAG,CAAC,CAAC;SACX;aAAM;YACH,gBAAgB;YAChB,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC;YAClC,IAAI,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAC;YACtC,OAAO;YACP,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;SACjC;IACL,CAAC;IAED;;OAEG;IACK,oDAAsB,GAA9B;QAAA,iBAiBC;QAhBG,SAAS;QACT,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,CAAC;SACpE;QAED,QAAQ;QACR,IAAI,IAAI,CAAC,eAAe,EAAE;YACtB,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC,sBAAsB,EAAE,IAAI,CAAC,CAAC;SAC5E;QAED,yBAAyB;QACzB,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,aAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,eAAM,CAAC,SAAS,GAAG,uBAAuB,EAAE,eAAM,CAAC,SAAS,GAAG,wBAAwB,EAAE;gBAClI,KAAI,CAAC,iBAAiB,EAAE,CAAC;YAC7B,CAAC,CAAC,CAAC;SACN;IACL,CAAC;IAED;;;OAGG;IACK,iDAAmB,GAA3B,UAA4B,WAAgB;QACxC,OAAO,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;QAC1C,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,WAAW,CAAC,CAAC;QAEzC,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE;YAC3B,OAAO,CAAC,KAAK,CAAC,yBAAyB,CAAC,CAAC;YACzC,OAAO;SACV;QAED,SAAS;QACT,OAAO,CAAC,GAAG,CAAC,yCAAyC,CAAC,CAAC;QACvD,IAAI,CAAC,mBAAmB,CAAC,MAAM,GAAG,IAAI,CAAC;QAEvC,2BAA2B;QAC3B,IAAM,SAAS,GAAG,WAAW,CAAC,SAAS,IAAI,WAAW,CAAC,OAAO,IAAI,WAAW,CAAC,KAAK,IAAI,WAAW,CAAC,UAAU,KAAK,CAAC,CAAC;QAIpH,IAAI,SAAS,EAAE;YACX,cAAc;YACd,IAAI,IAAI,CAAC,SAAS,EAAE;gBAChB,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,IAAI,CAAC;aAChC;YACD,IAAI,IAAI,CAAC,UAAU,EAAE;gBACjB,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,KAAK,CAAC;aAClC;SAEJ;aAAM;YACH,cAAc;YACd,IAAI,IAAI,CAAC,UAAU,EAAE;gBACjB,IAAI,CAAC,UAAU,CAAC,MAAM,GAAG,IAAI,CAAC;aACjC;YACD,IAAI,IAAI,CAAC,SAAS,EAAE;gBAChB,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,KAAK,CAAC;aACjC;SAEJ;IACL,CAAC;IAED;;OAEG;IACK,gDAAkB,GAA1B;QAGI,SAAS;QACT,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAE3B,uCAAuC;QACvC,iCAAiC;QAEjC,yBAAyB;QACzB,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IAChD,CAAC;IAED;;OAEG;IACK,oDAAsB,GAA9B;QAGI,SAAS;QACT,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAE3B,QAAQ;QACR,IAAM,SAAS,GAAG,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC;QACxC,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;QAEhC,uCAAuC;QACvC,iCAAiC;QAEjC,wBAAwB;QACxB,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAC,CAAC;IACxC,CAAC;IAED;;OAEG;IACK,+CAAiB,GAAzB;QACI,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAEvB,SAAS;QACT,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAE3B,kBAAkB;QAClB,IAAI,CAAC,mBAAmB,EAAE,CAAC;IAC/B,CAAC;IAED;;OAEG;IACK,iDAAmB,GAA3B;QACI,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC1B,IAAI,CAAC,mBAAmB,CAAC,MAAM,GAAG,KAAK,CAAC;SAC3C;IACL,CAAC;IAED;;;OAGG;IACK,iDAAmB,GAA3B,UAA4B,OAAe;QAGvC,IAAM,OAAO,GAAG;YACZ,OAAO,EAAE,OAAO;SACnB,CAAC;QAEF,mCAAgB,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,qBAAS,CAAC,sBAAsB,EAAE,OAAO,CAAC,CAAC;IACtF,CAAC;IAED;;OAEG;IACK,8CAAgB,GAAxB;QACI,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC3B,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC,qBAAqB,EAAE,IAAI,CAAC,CAAC;SAChF;IACL,CAAC;IAED;;OAEG;IACK,mDAAqB,GAA7B;QAEI,mCAAgB,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,qBAAS,CAAC,qBAAqB,EAAE,EAAE,CAAC,CAAC;IAChF,CAAC;IAED;;OAEG;IACK,4CAAc,GAAtB;QACI,+BAA+B;QAC/B,OAAO,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;IAClC,CAAC;IAED;;;OAGG;IACI,kDAAoB,GAA3B,UAA4B,aAA4C;QAAxE,iBA4CC;QAzCG,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;YACvB,EAAE,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;YAC3C,OAAO;SACV;QAED,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE;YAC/B,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YACvB,OAAO;SACV;QAED,IAAI,CAAC,aAAa,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,aAAa,CAAC,MAAM,KAAK,CAAC,EAAE;YAC/E,EAAE,CAAC,IAAI,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC;YACpC,OAAO;SACV;QAID,uBAAuB;QAEvB,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE;YAC1B,IAAM,aAAa,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC;YAEvC,aAAa;YACb,IAAI,CAAC,qBAAqB,CAAC,aAAa,CAAC,CAAC,EAAE,aAAa,CAAC,CAAC,CAAC,CAAC;SAChE;QAED,iBAAiB;QACjB,aAAa,CAAC,OAAO,CAAC,UAAC,QAAQ,EAAE,KAAK;YAElC,IAAI,KAAK,KAAK,CAAC,EAAE;gBACb,cAAc;gBAEd,KAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC;aACtD;iBAAM;gBACH,UAAU;gBACV,KAAI,CAAC,YAAY,CAAC;oBAEd,KAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC;gBACvD,CAAC,EAAE,KAAK,GAAG,GAAG,CAAC,CAAC;aACnB;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;OAIG;IACK,mDAAqB,GAA7B,UAA8B,CAAS,EAAE,CAAS;QAG9C,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;YACvB,EAAE,CAAC,KAAK,CAAC,8BAA8B,CAAC,CAAC;YACzC,OAAO;SACV;QAED,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE;YAC/B,EAAE,CAAC,KAAK,CAAC,sCAAsC,CAAC,CAAC;YACjD,OAAO;SACV;QAID,IAAI;YACA,uBAAuB;YACvB,IAAM,SAAS,GAAG,IAAI,CAAC,uBAAuB,CAAC,kBAAkB,CAC7D,CAAC,EAAE,CAAC,EACJ,IAAI,CAAC,eAAe,EACpB,eAAa,CAAC,SAAI,CAAG,CACxB,CAAC;YAEF,IAAI,SAAS,EAAE;gBACX,oBAAoB;gBACpB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;aAEvC;iBAAM;gBACH,EAAE,CAAC,KAAK,CAAC,gCAAU,CAAC,UAAK,CAAC,iGAAmB,CAAC,CAAC;aAClD;SACJ;QAAC,OAAO,KAAK,EAAE;YACZ,EAAE,CAAC,KAAK,CAAC,kFAAiB,EAAE,KAAK,CAAC,CAAC;SACtC;IACL,CAAC;IAED;;OAEG;IACI,6CAAe,GAAtB;QAGI,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,UAAC,IAAI,EAAE,KAAK;YACpC,IAAI,IAAI,IAAI,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;gBAC1B,EAAE,CAAC,GAAG,CAAC,iDAAW,KAAK,GAAG,CAAC,OAAG,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC3C,IAAI,CAAC,OAAO,EAAE,CAAC;aAClB;QACL,CAAC,CAAC,CAAC;QAEH,OAAO;QACP,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;IAE7B,CAAC;IAED;;OAEG;IACI,6CAAe,GAAtB;QAEI,IAAI,CAAC,eAAe,EAAE,CAAC;QACvB,kBAAkB;IACtB,CAAC;IAED,uCAAS,GAAT;QACI,SAAS;QACT,IAAI,CAAC,mCAAmC,EAAE,CAAC;QAE3C,UAAU;QACV,IAAI,CAAC,eAAe,EAAE,CAAC;QAEvB,SAAS;QACT,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,CAAC;SACrE;QACD,IAAI,IAAI,CAAC,eAAe,EAAE;YACtB,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,sBAAsB,EAAE,IAAI,CAAC,CAAC;SAC7E;QACD,+CAA+C;QAC/C,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC3B,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,qBAAqB,EAAE,IAAI,CAAC,CAAC;SACjF;IACL,CAAC;IA39BD;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;2DACS;IAI3B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;gEACc;IAIlC;QADC,QAAQ,CAAC,EAAE,CAAC,KAAK,CAAC;+DACa;IAIhC;QADC,QAAQ,CAAC,EAAE,CAAC,KAAK,CAAC;kEACgB;IAInC;QADC,QAAQ,CAAC,+BAAqB,CAAC;sEACoB;IAIpD;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;8DACY;IAI9B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;6DACW;IAI7B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;6DACW;IAI7B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;6DACW;IAG7B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;6DACW;IAG7B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;6DACW;IAG7B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;8DACY;IAG9B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;+DACa;IAI/B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;8DACY;IAG9B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;8DACY;IAG9B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;8DACY;IAG9B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;8DACY;IAG9B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;8DACY;IAG9B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;8DACY;IAI9B;QADC,QAAQ,CAAC,oCAA0B,CAAC;2EACyB;IAI9D;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;qEACmB;IAIvC;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;gEACc;IAOlC;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;oEACkB;IAGpC;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;4DACU;IAG5B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;2DACS;IAG3B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;0DACQ;IAG1B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;4DACU;IAG9B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;gEACc;IAGlC;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;2DACS;IAxGZ,mBAAmB;QADvC,OAAO;OACa,mBAAmB,CAg+BvC;IAAD,0BAAC;CAh+BD,AAg+BC,CAh+BgD,EAAE,CAAC,SAAS,GAg+B5D;kBAh+BoB,mBAAmB", "file": "", "sourceRoot": "/", "sourcesContent": ["// Learn TypeScript:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html\n// Learn Attribute:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html\n// Learn life-cycle callbacks:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html\n\nimport { ExtendLevelInfoResponse } from \"../bean/GameBean\";\nimport LeaveDialogController from \"../hall/LeaveDialogController\";\nimport { Tools } from \"../util/Tools\";\nimport { Config } from \"../util/Config\";\nimport GlobalManagerController, { PageType } from \"../GlobalManagerController\";\nimport SingleChessBoardController from \"../game/Chess/SingleChessBoardController\";\nimport { WebSocketManager } from \"../net/WebSocketManager\";\nimport { MessageId } from \"../net/MessageId\";\nimport { GameMgr } from \"../common/GameMgr\";\nimport { EventType } from \"../common/EventCenter\";\nimport { ReceivedMessageBean } from \"../net/MessageBaseBean\";\n\nconst { ccclass, property } = cc._decorator;\n\n@ccclass\nexport default class LevelPageController extends cc.Component {\n\n    // 返回按钮\n    @property(cc.Node)\n    backButton: cc.Node = null;\n\n    // 开始游戏按钮\n    @property(cc.Button)\n    startGameButton: cc.Button = null;\n\n    // 地雷数UI标签\n    @property(cc.Label)\n    mineCountLabel: cc.Label = null;\n\n    // 当前关卡数UI标签\n    @property(cc.Label)\n    currentLevelLabel: cc.Label = null;\n\n    // 退出游戏弹窗\n    @property(LeaveDialogController)\n    leaveDialogController: LeaveDialogController = null;\n\n    // level_page节点\n    @property(cc.Node)\n    levelPageNode: cc.Node = null;\n\n    // game_map_1节点\n    @property(cc.Node)\n    gameMap1Node: cc.Node = null;\n\n    // game_map_2节点\n    @property(cc.Node)\n    gameMap2Node: cc.Node = null;\n\n    // 方形地图节点引用\n    @property(cc.Node)\n    qipan8x8Node: cc.Node = null; // level_page/game_map_1/chess_bg/qipan8*8\n\n    @property(cc.Node)\n    qipan8x9Node: cc.Node = null; // level_page/game_map_1/chess_bg/qipan8*9\n\n    @property(cc.Node)\n    qipan9x9Node: cc.Node = null; // level_page/game_map_1/chess_bg/qipan9*9\n\n    @property(cc.Node)\n    qipan9x10Node: cc.Node = null; // level_page/game_map_1/chess_bg/qipan9*10\n\n    @property(cc.Node)\n    qipan10x10Node: cc.Node = null; // level_page/game_map_1/chess_bg/qipan10*10\n\n    // 特殊关卡节点引用\n    @property(cc.Node)\n    levelS001Node: cc.Node = null; // level_page/game_map_2/game_bg/Level_S001\n\n    @property(cc.Node)\n    levelS002Node: cc.Node = null; // level_page/game_map_2/game_bg/Level_S002\n\n    @property(cc.Node)\n    levelS003Node: cc.Node = null; // level_page/game_map_2/game_bg/Level_S003\n\n    @property(cc.Node)\n    levelS004Node: cc.Node = null; // level_page/game_map_2/game_bg/Level_S004\n\n    @property(cc.Node)\n    levelS005Node: cc.Node = null; // level_page/game_map_2/game_bg/Level_S005\n\n    @property(cc.Node)\n    levelS006Node: cc.Node = null; // level_page/game_map_2/game_bg/Level_S006\n\n    // 单机模式棋盘控制器\n    @property(SingleChessBoardController)\n    singleChessBoardController: SingleChessBoardController = null;\n\n    // 测试按钮（用于调试显示地雷位置）\n    @property(cc.Button)\n    debugShowMinesButton: cc.Button = null;\n\n    // 测试预制体（用于显示地雷位置）\n    @property(cc.Prefab)\n    debugMinePrefab: cc.Prefab = null;\n\n    // 存储创建的测试预制体节点，用于清理\n    private debugMineNodes: cc.Node[] = [];\n\n    // 结算页面相关节点\n    @property(cc.Node)\n    levelSettlementNode: cc.Node = null; // level_settlement节点\n\n    @property(cc.Node)\n    boardBgNode: cc.Node = null; // level_settlement/board_bg节点\n\n    @property(cc.Node)\n    loseBgNode: cc.Node = null; // level_settlement/board_bg/lose_bg节点\n\n    @property(cc.Node)\n    winBgNode: cc.Node = null; // level_settlement/board_bg/win_bg节点\n\n    @property(cc.Button)\n    retryButton: cc.Button = null; // 再来一次按钮\n\n    @property(cc.Button)\n    nextLevelButton: cc.Button = null; // 下一关按钮\n\n    @property(cc.Button)\n    exitButton: cc.Button = null; // 退出按钮\n\n    // 当前关卡数据\n    private currentLevel: number = 1;\n    private currentLevelInfo: ExtendLevelInfoResponse = null;\n    private currentRoomId: number = 0; // 当前关卡游戏的房间ID\n    private currentSingleChessBoard: SingleChessBoardController = null; // 当前激活的单机棋盘\n\n    // 记录最后一次点击是否是炸弹\n    private lastClickWasBomb: boolean = false;\n\n    // 性能优化相关\n    private lastShownMapNode: cc.Node = null; // 记录上次显示的地图节点\n    private isUpdating: boolean = false; // 防止重复更新\n\n    onLoad() {\n        // 设置返回按钮点击事件 - 使用与GamePageController相同的样式\n        if (this.backButton) {\n            Tools.imageButtonClick(this.backButton, Config.buttonRes + 'side_btn_back_normal', Config.buttonRes + 'side_btn_back_pressed', () => {\n                this.onBackButtonClick();\n            });\n        }\n\n        // 设置开始游戏按钮点击事件\n        if (this.startGameButton) {\n            this.startGameButton.node.on('click', this.onStartGameButtonClick, this);\n        }\n\n        // 注册单机模式消息监听\n        this.registerSingleModeMessageHandlers();\n\n        // 设置结算页面按钮事件\n        this.setupSettlementButtons();\n\n        // 注意：不在onLoad中初始化关卡数UI，等待外部调用setCurrentLevel时再更新\n    }\n\n    start() {\n        // 初始化时隐藏所有地图节点\n        this.hideAllMapNodes();\n\n        // 设置测试按钮点击事件\n        this.setupDebugButton();\n    }\n\n    /**\n     * 返回按钮点击事件\n     */\n    private onBackButtonClick() {\n        // 如果没有有效的房间ID，说明还没有进入游戏，直接返回关卡选择页面\n        if (this.currentRoomId <= 0) {\n           \n            this.returnToLevelSelect();\n            return;\n        }\n\n        // 弹出确认退出对话框，type=1表示退出本局游戏，传递当前房间ID\n        if (this.leaveDialogController) {\n\n            this.leaveDialogController.show(1, () => {\n\n            }, this.currentRoomId);\n        } else {\n            cc.warn(\"LeaveDialogController 未配置\");\n        }\n    }\n\n    /**\n     * 返回到关卡选择页面\n     */\n    private returnToLevelSelect() {\n        // 查找GlobalManagerController并切换到大厅页面\n        const globalManagerNode = cc.find(\"Canvas/global_node\") || cc.find(\"global_node\");\n        if (globalManagerNode) {\n            const globalManager = globalManagerNode.getComponent(GlobalManagerController);\n            if (globalManager) {\n                globalManager.setCurrentPage(PageType.HALL_PAGE);\n            }\n        }\n    }\n\n    /**\n     * 开始游戏按钮点击事件\n     */\n    private onStartGameButtonClick() {\n        // ExtendLevelInfo消息现在由LevelSelectPageController发送\n        // 这里直接进入游戏，等待后端响应\n        \n    }\n\n    /**\n     * 处理ExtendLevelInfo响应\n     * @param levelInfo 关卡信息响应数据\n     */\n    public onExtendLevelInfo(levelInfo: ExtendLevelInfoResponse) {\n       \n        this.currentLevelInfo = levelInfo;\n\n        // 保存房间ID，用于退出时使用\n        if (levelInfo.roomId) {\n            this.currentRoomId = levelInfo.roomId;\n        }\n\n        // 重置关卡状态（包括清除测试预制体）\n        this.resetLevelState();\n\n        // 重置炸弹点击标记\n        this.lastClickWasBomb = false;\n\n        // 开始新游戏时，先重置当前棋盘（清理上一局的痕迹）\n        if (this.currentSingleChessBoard) {\n\n            this.currentSingleChessBoard.resetBoard();\n        }\n\n        // 更新地雷数UI\n        this.updateMineCountUI(levelInfo.mineCount);\n\n        // 使用当前设置的关卡编号，而不是后端返回的levelId\n        // 因为后端的levelId可能与前端的关卡编号不一致\n\n        this.enterLevel(this.currentLevel);\n\n        // 通知当前激活的单机棋盘控制器处理ExtendLevelInfo\n        if (this.currentSingleChessBoard) {\n            this.currentSingleChessBoard.onExtendLevelInfo();\n        }\n    }\n\n    /**\n     * 更新地雷数UI\n     * @param mineCount 地雷数量\n     */\n    private updateMineCountUI(mineCount: number) {\n        if (this.mineCountLabel) {\n            this.mineCountLabel.string = mineCount.toString();\n           \n        }\n    }\n\n    /**\n     * 更新当前关卡数UI\n     * @param levelNumber 关卡编号\n     */\n    private updateCurrentLevelUI(levelNumber: number) {\n        if (this.currentLevelLabel) {\n            this.currentLevelLabel.string = `第${levelNumber}关`;\n           \n        }\n    }\n\n    /**\n     * 根据关卡数进入相应的关卡（优化版本）\n     * @param levelNumber 关卡编号\n     */\n    private enterLevel(levelNumber: number) {\n        // 防止重复更新\n        if (this.isUpdating) {\n            return;\n        }\n        this.isUpdating = true;\n\n        // 更新关卡数UI显示\n        this.updateCurrentLevelUI(levelNumber);\n\n        // 获取目标地图节点和容器\n        const targetMapInfo = this.getMapNodeByLevel(levelNumber);\n        if (!targetMapInfo) {\n            cc.warn(`未知的关卡编号: ${levelNumber}`);\n            this.isUpdating = false;\n            return;\n        }\n\n        // 只有当目标节点与当前显示的节点不同时才进行切换\n        if (this.lastShownMapNode !== targetMapInfo.mapNode) {\n            // 隐藏上一个显示的地图节点\n            if (this.lastShownMapNode) {\n                this.lastShownMapNode.active = false;\n            }\n\n            // 显示目标容器和地图节点\n            this.showMapContainer(targetMapInfo.containerType);\n            this.showMapNodeOptimized(targetMapInfo.mapNode, targetMapInfo.mapName);\n\n            // 记录当前显示的节点\n            this.lastShownMapNode = targetMapInfo.mapNode;\n        }\n\n        // 设置当前激活的单机棋盘控制器\n        if (this.singleChessBoardController) {\n            // 根据关卡设置棋盘类型\n            const boardType = this.getBoardTypeByLevel(levelNumber);\n            this.singleChessBoardController.initBoard(boardType);\n            this.currentSingleChessBoard = this.singleChessBoardController;\n        } else {\n            this.currentSingleChessBoard = null;\n        }\n\n        this.isUpdating = false;\n    }\n\n    /**\n     * 根据关卡数获取对应的地图节点信息\n     * @param levelNumber 关卡编号\n     */\n    private getMapNodeByLevel(levelNumber: number): {mapNode: cc.Node, mapName: string, containerType: 'map1' | 'map2'} | null {\n        if (levelNumber >= 1 && levelNumber <= 4) {\n            return {mapNode: this.qipan8x8Node, mapName: \"qipan8*8\", containerType: 'map1'};\n        } else if (levelNumber === 5) {\n            return {mapNode: this.levelS001Node, mapName: \"Level_S001\", containerType: 'map2'};\n        } else if (levelNumber >= 6 && levelNumber <= 9) {\n            return {mapNode: this.qipan8x9Node, mapName: \"qipan8*9\", containerType: 'map1'};\n        } else if (levelNumber === 10) {\n            return {mapNode: this.levelS002Node, mapName: \"Level_S002\", containerType: 'map2'};\n        } else if (levelNumber >= 11 && levelNumber <= 14) {\n            return {mapNode: this.qipan9x9Node, mapName: \"qipan9*9\", containerType: 'map1'};\n        } else if (levelNumber === 15) {\n            return {mapNode: this.levelS003Node, mapName: \"Level_S003\", containerType: 'map2'};\n        } else if (levelNumber >= 16 && levelNumber <= 19) {\n            return {mapNode: this.qipan9x10Node, mapName: \"qipan9*10\", containerType: 'map1'};\n        } else if (levelNumber === 20) {\n            return {mapNode: this.levelS004Node, mapName: \"Level_S004\", containerType: 'map2'};\n        } else if (levelNumber >= 21 && levelNumber <= 24) {\n            return {mapNode: this.qipan10x10Node, mapName: \"qipan10*10\", containerType: 'map1'};\n        } else if (levelNumber === 25) {\n            return {mapNode: this.levelS005Node, mapName: \"Level_S005\", containerType: 'map2'};\n        } else if (levelNumber >= 26 && levelNumber <= 29) {\n            return {mapNode: this.qipan10x10Node, mapName: \"qipan10*10\", containerType: 'map1'};\n        } else if (levelNumber === 30) {\n            return {mapNode: this.levelS006Node, mapName: \"Level_S006\", containerType: 'map2'};\n        }\n        return null;\n    }\n\n\n\n    /**\n     * 根据关卡编号获取棋盘类型\n     * @param levelNumber 关卡编号\n     */\n    private getBoardTypeByLevel(levelNumber: number): string {\n        if (levelNumber >= 1 && levelNumber <= 4) {\n            return \"8x8\";\n        } else if (levelNumber >= 6 && levelNumber <= 9) {\n            return \"8x9\";\n        } else if (levelNumber >= 11 && levelNumber <= 14) {\n            return \"9x9\";\n        } else if (levelNumber >= 16 && levelNumber <= 19) {\n            return \"9x10\";\n        } else if (levelNumber >= 21 && levelNumber <= 24 || levelNumber >= 26 && levelNumber <= 29) {\n            return \"10x10\";\n        }\n        return \"8x8\"; // 默认\n    }\n\n    /**\n     * 显示指定的地图节点（优化版本）\n     * @param mapNode 要显示的地图节点\n     * @param mapName 地图名称（用于日志）\n     */\n    private showMapNodeOptimized(mapNode: cc.Node, mapName: string) {\n        if (mapNode) {\n            mapNode.active = true;\n        } else {\n            cc.warn(`❌ 地图节点未找到: ${mapName}`);\n        }\n    }\n\n    /**\n     * 显示指定的地图容器\n     * @param containerType 容器类型\n     */\n    private showMapContainer(containerType: 'map1' | 'map2') {\n        // 确保 level_page 节点是激活的\n        if (this.levelPageNode) {\n            this.levelPageNode.active = true;\n        }\n\n        // 根据容器类型显示对应容器，隐藏另一个\n        if (containerType === 'map1') {\n            if (this.gameMap1Node && !this.gameMap1Node.active) {\n                this.gameMap1Node.active = true;\n            }\n            if (this.gameMap2Node && this.gameMap2Node.active) {\n                this.gameMap2Node.active = false;\n            }\n        } else {\n            if (this.gameMap2Node && !this.gameMap2Node.active) {\n                this.gameMap2Node.active = true;\n            }\n            if (this.gameMap1Node && this.gameMap1Node.active) {\n                this.gameMap1Node.active = false;\n            }\n        }\n    }\n\n    /**\n     * 隐藏所有地图节点\n     */\n    private hideAllMapNodes() {\n        const allMapNodes = [\n            this.qipan8x8Node,\n            this.qipan8x9Node,\n            this.qipan9x9Node,\n            this.qipan9x10Node,\n            this.qipan10x10Node,\n            this.levelS001Node,\n            this.levelS002Node,\n            this.levelS003Node,\n            this.levelS004Node,\n            this.levelS005Node,\n            this.levelS006Node\n        ];\n\n        allMapNodes.forEach(node => {\n            if (node) {\n                node.active = false;\n            }\n        });\n    }\n\n    /**\n     * 设置当前关卡（从外部调用）\n     * @param levelNumber 关卡编号\n     */\n    public setCurrentLevel(levelNumber: number) {\n       \n\n        // 重置关卡状态（包括清除测试预制体）\n        this.resetLevelState();\n\n        this.currentLevel = levelNumber;\n\n        // 立即根据关卡数切换地图显示\n        this.enterLevel(levelNumber);\n    }\n\n    /**\n     * 获取当前关卡编号\n     */\n    public getCurrentLevel(): number {\n        return this.currentLevel;\n    }\n\n    /**\n     * 获取当前关卡信息\n     */\n    public getCurrentLevelInfo(): ExtendLevelInfoResponse {\n        return this.currentLevelInfo;\n    }\n\n    /**\n     * 获取当前房间ID\n     */\n    public getCurrentRoomId(): number {\n        return this.currentRoomId;\n    }\n\n    /**\n     * 隐藏所有地图容器\n     */\n    private hideAllMapContainers() {\n        // 确保 level_page 节点是激活的\n        if (this.levelPageNode) {\n            this.levelPageNode.active = true;\n          \n        }\n\n        // 隐藏两个主要的地图容器\n        if (this.gameMap1Node) {\n            this.gameMap1Node.active = false;\n            \n        }\n        if (this.gameMap2Node) {\n            this.gameMap2Node.active = false;\n           \n        }\n\n        // 同时隐藏所有具体的地图节点\n        this.hideAllMapNodes();\n    }\n\n    /**\n     * 显示 game_map_1 容器（方形地图）\n     */\n    private showGameMap1() {\n        if (this.gameMap1Node) {\n            this.gameMap1Node.active = true;\n            \n        } else {\n            cc.warn(\"❌ game_map_1 节点未找到\");\n        }\n    }\n\n    /**\n     * 显示 game_map_2 容器（特殊关卡）\n     */\n    private showGameMap2() {\n        if (this.gameMap2Node) {\n            this.gameMap2Node.active = true;\n\n        } else {\n            cc.warn(\"❌ game_map_2 节点未找到\");\n        }\n    }\n\n    /**\n     * 获取当前激活的单机棋盘控制器\n     */\n    public getCurrentSingleChessBoard(): SingleChessBoardController | null {\n        return this.currentSingleChessBoard;\n    }\n\n    /**\n     * 处理单机模式的点击响应\n     * @param response 点击响应数据\n     */\n    public handleSingleModeClickResponse(response: any) {\n        if (this.currentSingleChessBoard) {\n            const { x, y, result, chainReaction } = response;\n\n            // 处理点击结果\n            if (x !== undefined && y !== undefined && result !== undefined) {\n                this.currentSingleChessBoard.handleClickResponse(x, y, result);\n            }\n\n            // 处理连锁反应\n            if (chainReaction && Array.isArray(chainReaction)) {\n                this.currentSingleChessBoard.handleChainReaction(chainReaction);\n            }\n        }\n    }\n\n    /**\n     * 处理单机模式游戏结束\n     * @param gameEndData 游戏结束数据\n     */\n    public handleSingleModeGameEnd(gameEndData: any) {\n        if (this.currentSingleChessBoard) {\n            // 禁用棋盘触摸\n            this.currentSingleChessBoard.disableAllGridTouch();\n\n            // 处理游戏结束\n            this.currentSingleChessBoard.onLevelGameEnd();\n        }\n    }\n\n    /**\n     * 重置当前单机棋盘（仅在开始新游戏时调用）\n     */\n    public resetCurrentSingleChessBoard() {\n      \n        if (this.currentSingleChessBoard) {\n            // 重置棋盘状态（清理所有预制体和格子状态）\n            this.currentSingleChessBoard.resetBoard();\n\n            // 重新启用触摸事件\n            this.currentSingleChessBoard.enableAllGridTouch();\n        }\n    }\n\n    /**\n     * 注册单机模式消息处理器\n     */\n    private registerSingleModeMessageHandlers() {\n        // 监听WebSocket消息\n        GameMgr.Event.AddEventListener(EventType.ReceiveMessage, this.onReceiveMessage, this);\n    }\n\n    /**\n     * 取消单机模式消息监听\n     */\n    private unregisterSingleModeMessageHandlers() {\n        GameMgr.Event.RemoveEventListener(EventType.ReceiveMessage, this.onReceiveMessage, this);\n    }\n\n    /**\n     * 处理接收到的WebSocket消息\n     * @param messageBean 消息数据\n     */\n    private onReceiveMessage(messageBean: ReceivedMessageBean) {\n        switch (messageBean.msgId) {\n            case MessageId.MsgTypeLevelClickBlock:\n                this.onLevelClickBlockResponse(messageBean.data);\n                break;\n            case MessageId.MsgTypeLevelGameEnd:\n                this.onLevelGameEnd(messageBean.data);\n                break;\n        }\n    }\n\n    /**\n     * 处理LevelClickBlock响应\n     * @param response 点击响应数据\n     */\n    public onLevelClickBlockResponse(response: any) {\n        \n\n        if (this.currentSingleChessBoard) {\n            // 解构响应数据，支持多种可能的字段名\n            const {\n                x, y, result, action,\n                chainReaction, revealedGrids, floodFill, revealedBlocks,\n                floodFillResults  // 单机模式使用这个字段\n            } = response;\n\n            // 根据action类型处理不同的响应\n            if (x !== undefined && y !== undefined && result !== undefined) {\n\n                if (action === 2) {\n                    // 标记/取消标记操作，不调用handleClickResponse，避免格子消失\n\n                    // 不调用 handleClickResponse，因为标记操作不应该隐藏格子\n                } else if (action === 1) {\n                    // 挖掘操作\n                    console.log(`🎯 点击响应: (${x}, ${y}), 结果: ${result}`);\n\n                    // 检查是否点到炸弹\n                    if (result === \"boom\" || result === \"mine\") {\n                        console.log(\"💣 检测到炸弹点击，设置 lastClickWasBomb = true\");\n                        this.lastClickWasBomb = true;\n                    }\n\n                    this.currentSingleChessBoard.handleClickResponse(x, y, result);\n\n                    // 处理连锁展开数据\n                    if (floodFillResults && Array.isArray(floodFillResults) && floodFillResults.length > 0) {\n                        \n                        this.currentSingleChessBoard.handleFloodFillResults(floodFillResults);\n                    }\n                } else {\n                    // 其他操作，默认按挖掘处理\n\n                    this.currentSingleChessBoard.handleClickResponse(x, y, result);\n                }\n            }\n\n         \n\n            \n\n            \n        }\n    }\n\n    /**\n     * 处理LevelGameEnd通知\n     * @param gameEndData 游戏结束数据\n     */\n    public onLevelGameEnd(gameEndData: any) {\n\n\n        if (this.currentSingleChessBoard) {\n            // 禁用棋盘触摸\n            this.currentSingleChessBoard.disableAllGridTouch();\n\n            // 处理游戏结束（不清理数据）\n            this.currentSingleChessBoard.onLevelGameEnd();\n        }\n\n        // 检查是否点到了炸弹，如果是游戏失败且点到炸弹，则延迟显示结算页面\n        const isGameFailed = !gameEndData.success;\n        const hasBombExploded = this.lastClickWasBomb ||\n                               (this.currentSingleChessBoard && this.currentSingleChessBoard.hasBombExplodedInThisGame());\n\n        console.log(\"=== 游戏结束延迟检查 ===\");\n        console.log(\"gameEndData:\", gameEndData);\n        console.log(\"isGameFailed:\", isGameFailed);\n        console.log(\"lastClickWasBomb:\", this.lastClickWasBomb);\n        console.log(\"currentSingleChessBoard:\", !!this.currentSingleChessBoard);\n        console.log(\"hasBombExploded:\", hasBombExploded);\n\n        if (isGameFailed && hasBombExploded) {\n            // 点到炸弹导致的游戏失败，延迟1.5秒显示结算页面\n            console.log(\"✅ 满足延迟条件：点到炸弹导致游戏失败，延迟1.5秒显示结算页面\");\n            this.scheduleOnce(() => {\n                console.log(\"⏰ 延迟时间到，现在显示结算页面\");\n                this.showLevelSettlement(gameEndData);\n                // 重置标记\n                this.lastClickWasBomb = false;\n            }, 1.5);\n        } else {\n            // 其他情况，立即显示结算页面\n            console.log(\"❌ 不满足延迟条件，立即显示结算页面\");\n            this.showLevelSettlement(gameEndData);\n            // 重置标记\n            this.lastClickWasBomb = false;\n        }\n    }\n\n    /**\n     * 设置结算页面按钮事件\n     */\n    private setupSettlementButtons() {\n        // 再来一次按钮\n        if (this.retryButton) {\n            this.retryButton.node.on('click', this.onRetryButtonClick, this);\n        }\n\n        // 下一关按钮\n        if (this.nextLevelButton) {\n            this.nextLevelButton.node.on('click', this.onNextLevelButtonClick, this);\n        }\n\n        // 退出按钮 - 使用按压效果，模仿其他返回按钮\n        if (this.exitButton) {\n            Tools.imageButtonClick(this.exitButton.node, Config.buttonRes + 'board_btn_back_normal', Config.buttonRes + 'board_btn_back_pressed', () => {\n                this.onExitButtonClick();\n            });\n        }\n    }\n\n    /**\n     * 显示结算页面\n     * @param gameEndData 游戏结束数据\n     */\n    private showLevelSettlement(gameEndData: any) {\n        console.log(\"🎯 showLevelSettlement 被调用\");\n        console.log(\"gameEndData:\", gameEndData);\n\n        if (!this.levelSettlementNode) {\n            console.error(\"levelSettlementNode 未配置\");\n            return;\n        }\n\n        // 显示结算页面\n        console.log(\"📱 设置 levelSettlementNode.active = true\");\n        this.levelSettlementNode.active = true;\n\n        // 根据游戏结果显示对应的背景 - 修复成功判断逻辑\n        const isSuccess = gameEndData.isSuccess || gameEndData.success || gameEndData.isWin || gameEndData.gameStatus === 1;\n\n       \n\n        if (isSuccess) {\n            // 成功 - 显示胜利背景\n            if (this.winBgNode) {\n                this.winBgNode.active = true;\n            }\n            if (this.loseBgNode) {\n                this.loseBgNode.active = false;\n            }\n            \n        } else {\n            // 失败 - 显示失败背景\n            if (this.loseBgNode) {\n                this.loseBgNode.active = true;\n            }\n            if (this.winBgNode) {\n                this.winBgNode.active = false;\n            }\n           \n        }\n    }\n\n    /**\n     * 再来一次按钮点击事件\n     */\n    private onRetryButtonClick() {\n       \n\n        // 关闭结算页面\n        this.hideLevelSettlement();\n\n        // 注意：不在这里重置棋盘，等收到ExtendLevelInfo响应时再重置\n        // 这样可以避免过早清理，让玩家在点击按钮后还能短暂看到游玩痕迹\n\n        // 发送当前关卡的ExtendLevelInfo\n        this.sendExtendLevelInfo(this.currentLevel);\n    }\n\n    /**\n     * 下一关按钮点击事件\n     */\n    private onNextLevelButtonClick() {\n\n\n        // 关闭结算页面\n        this.hideLevelSettlement();\n\n        // 进入下一关\n        const nextLevel = this.currentLevel + 1;\n        this.setCurrentLevel(nextLevel);\n\n        // 注意：不在这里重置棋盘，等收到ExtendLevelInfo响应时再重置\n        // 这样可以避免过早清理，让玩家在点击按钮后还能短暂看到游玩痕迹\n\n        // 发送下一关的ExtendLevelInfo\n        this.sendExtendLevelInfo(nextLevel);\n    }\n\n    /**\n     * 退出按钮点击事件\n     */\n    private onExitButtonClick() {\n        console.log(\"退出按钮被点击\");\n\n        // 关闭结算页面\n        this.hideLevelSettlement();\n\n        // 返回到关卡选择页面（匹配界面）\n        this.returnToLevelSelect();\n    }\n\n    /**\n     * 隐藏结算页面\n     */\n    private hideLevelSettlement() {\n        if (this.levelSettlementNode) {\n            this.levelSettlementNode.active = false;\n        }\n    }\n\n    /**\n     * 发送ExtendLevelInfo消息\n     * @param levelId 关卡ID\n     */\n    private sendExtendLevelInfo(levelId: number) {\n       \n\n        const request = {\n            levelId: levelId\n        };\n\n        WebSocketManager.GetInstance().sendMsg(MessageId.MsgTypeExtendLevelInfo, request);\n    }\n\n    /**\n     * 设置测试按钮\n     */\n    private setupDebugButton() {\n        if (this.debugShowMinesButton) {\n            this.debugShowMinesButton.node.on('click', this.onDebugShowMinesClick, this);\n        }\n    }\n\n    /**\n     * 测试按钮点击事件 - 发送DebugShowMines消息\n     */\n    private onDebugShowMinesClick() {\n       \n        WebSocketManager.GetInstance().sendMsg(MessageId.MsgTypeDebugShowMines, {});\n    }\n\n    /**\n     * 判断是否在单机模式\n     */\n    private isInSingleMode(): boolean {\n        // 单机模式的判断逻辑：当前页面是关卡页面且有有效的房间ID\n        return this.currentRoomId > 0;\n    }\n\n    /**\n     * 处理DebugShowMines响应，在炸弹位置生成测试预制体\n     * @param minePositions 炸弹位置数组 [{x: number, y: number}]\n     */\n    public handleDebugShowMines(minePositions: Array<{x: number, y: number}>) {\n      \n\n        if (!this.debugMinePrefab) {\n            cc.warn(\"debugMinePrefab 预制体未设置，无法显示测试标记\");\n            return;\n        }\n\n        if (!this.currentSingleChessBoard) {\n            cc.warn(\"当前没有激活的单机棋盘\");\n            return;\n        }\n\n        if (!minePositions || !Array.isArray(minePositions) || minePositions.length === 0) {\n            cc.warn(\"地雷位置数据无效:\", minePositions);\n            return;\n        }\n\n      \n\n        // 先尝试直接创建一个测试预制体，不使用延迟\n       \n        if (minePositions.length > 0) {\n            const firstPosition = minePositions[0];\n           \n            // 直接调用，不使用延迟\n            this.createDebugMinePrefab(firstPosition.x, firstPosition.y);\n        }\n\n        // 在每个炸弹位置生成测试预制体\n        minePositions.forEach((position, index) => {\n            \n            if (index === 0) {\n                // 第一个不延迟，立即执行\n              \n                this.createDebugMinePrefab(position.x, position.y);\n            } else {\n                // 其他的使用延迟\n                this.scheduleOnce(() => {\n                    \n                    this.createDebugMinePrefab(position.x, position.y);\n                }, index * 0.1);\n            }\n        });\n    }\n\n    /**\n     * 在指定位置创建测试预制体\n     * @param x 格子x坐标\n     * @param y 格子y坐标\n     */\n    private createDebugMinePrefab(x: number, y: number) {\n       \n\n        if (!this.debugMinePrefab) {\n            cc.error(\"debugMinePrefab 为空，无法创建测试预制体\");\n            return;\n        }\n\n        if (!this.currentSingleChessBoard) {\n            cc.error(\"currentSingleChessBoard 为空，无法创建测试预制体\");\n            return;\n        }\n\n        \n\n        try {\n            // 使用棋盘控制器的公共方法创建自定义预制体\n            const debugNode = this.currentSingleChessBoard.createCustomPrefab(\n                x, y,\n                this.debugMinePrefab,\n                `DebugMine_${x}_${y}`\n            );\n\n            if (debugNode) {\n                // 将创建的节点存储起来，用于后续清理\n                this.debugMineNodes.push(debugNode);\n                \n            } else {\n                cc.error(`❌ 在位置 (${x}, ${y}) 创建测试预制体失败，返回值为空`);\n            }\n        } catch (error) {\n            cc.error(`❌ 创建测试预制体时发生错误:`, error);\n        }\n    }\n\n    /**\n     * 清除所有测试预制体\n     */\n    public clearDebugMines() {\n       \n\n        this.debugMineNodes.forEach((node, index) => {\n            if (node && cc.isValid(node)) {\n                cc.log(`清除测试预制体 ${index + 1}:`, node.name);\n                node.destroy();\n            }\n        });\n\n        // 清空数组\n        this.debugMineNodes = [];\n       \n    }\n\n    /**\n     * 重置关卡状态（包括清除测试预制体）\n     */\n    public resetLevelState() {\n       \n        this.clearDebugMines();\n        // 这里可以添加其他需要重置的状态\n    }\n\n    onDestroy() {\n        // 取消消息监听\n        this.unregisterSingleModeMessageHandlers();\n\n        // 清理测试预制体\n        this.clearDebugMines();\n\n        // 清理按钮事件\n        if (this.retryButton) {\n            this.retryButton.node.off('click', this.onRetryButtonClick, this);\n        }\n        if (this.nextLevelButton) {\n            this.nextLevelButton.node.off('click', this.onNextLevelButtonClick, this);\n        }\n        // 退出按钮使用 Tools.imageButtonClick，会自动管理事件，无需手动清理\n        if (this.debugShowMinesButton) {\n            this.debugShowMinesButton.node.off('click', this.onDebugShowMinesClick, this);\n        }\n    }\n}\n"]}