
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/level/LevelPageController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'c8e13uMxpxGELez69T2SfJx', 'LevelPageController');
// scripts/level/LevelPageController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var LeaveDialogController_1 = require("../hall/LeaveDialogController");
var Tools_1 = require("../util/Tools");
var Config_1 = require("../util/Config");
var GlobalManagerController_1 = require("../GlobalManagerController");
var SingleChessBoardController_1 = require("../game/Chess/SingleChessBoardController");
var WebSocketManager_1 = require("../net/WebSocketManager");
var MessageId_1 = require("../net/MessageId");
var GameMgr_1 = require("../common/GameMgr");
var EventCenter_1 = require("../common/EventCenter");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var LevelPageController = /** @class */ (function (_super) {
    __extends(LevelPageController, _super);
    function LevelPageController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        // 返回按钮
        _this.backButton = null;
        // 开始游戏按钮
        _this.startGameButton = null;
        // 地雷数UI标签
        _this.mineCountLabel = null;
        // 当前关卡数UI标签
        _this.currentLevelLabel = null;
        // 退出游戏弹窗
        _this.leaveDialogController = null;
        // level_page节点
        _this.levelPageNode = null;
        // game_map_1节点
        _this.gameMap1Node = null;
        // game_map_2节点
        _this.gameMap2Node = null;
        // 方形地图节点引用
        _this.qipan8x8Node = null; // level_page/game_map_1/chess_bg/qipan8*8
        _this.qipan8x9Node = null; // level_page/game_map_1/chess_bg/qipan8*9
        _this.qipan9x9Node = null; // level_page/game_map_1/chess_bg/qipan9*9
        _this.qipan9x10Node = null; // level_page/game_map_1/chess_bg/qipan9*10
        _this.qipan10x10Node = null; // level_page/game_map_1/chess_bg/qipan10*10
        // 特殊关卡节点引用
        _this.levelS001Node = null; // level_page/game_map_2/game_bg/Level_S001
        _this.levelS002Node = null; // level_page/game_map_2/game_bg/Level_S002
        _this.levelS003Node = null; // level_page/game_map_2/game_bg/Level_S003
        _this.levelS004Node = null; // level_page/game_map_2/game_bg/Level_S004
        _this.levelS005Node = null; // level_page/game_map_2/game_bg/Level_S005
        _this.levelS006Node = null; // level_page/game_map_2/game_bg/Level_S006
        // 单机模式棋盘控制器
        _this.singleChessBoardController = null;
        // 测试按钮（用于调试显示地雷位置）
        _this.debugShowMinesButton = null;
        // 测试预制体（用于显示地雷位置）
        _this.debugMinePrefab = null;
        // 存储创建的测试预制体节点，用于清理
        _this.debugMineNodes = [];
        // 结算页面相关节点
        _this.levelSettlementNode = null; // level_settlement节点
        _this.boardBgNode = null; // level_settlement/board_bg节点
        _this.loseBgNode = null; // level_settlement/board_bg/lose_bg节点
        _this.winBgNode = null; // level_settlement/board_bg/win_bg节点
        _this.retryButton = null; // 再来一次按钮
        _this.nextLevelButton = null; // 下一关按钮
        _this.exitButton = null; // 退出按钮
        // 当前关卡数据
        _this.currentLevel = 1;
        _this.currentLevelInfo = null;
        _this.currentRoomId = 0; // 当前关卡游戏的房间ID
        _this.currentSingleChessBoard = null; // 当前激活的单机棋盘
        // 记录最后一次点击是否是炸弹
        _this.lastClickWasBomb = false;
        // 性能优化相关
        _this.lastShownMapNode = null; // 记录上次显示的地图节点
        _this.isUpdating = false; // 防止重复更新
        return _this;
    }
    LevelPageController.prototype.onLoad = function () {
        var _this = this;
        // 设置返回按钮点击事件 - 使用与GamePageController相同的样式
        if (this.backButton) {
            Tools_1.Tools.imageButtonClick(this.backButton, Config_1.Config.buttonRes + 'side_btn_back_normal', Config_1.Config.buttonRes + 'side_btn_back_pressed', function () {
                _this.onBackButtonClick();
            });
        }
        // 设置开始游戏按钮点击事件
        if (this.startGameButton) {
            this.startGameButton.node.on('click', this.onStartGameButtonClick, this);
        }
        // 注册单机模式消息监听
        this.registerSingleModeMessageHandlers();
        // 设置结算页面按钮事件
        this.setupSettlementButtons();
        // 注意：不在onLoad中初始化关卡数UI，等待外部调用setCurrentLevel时再更新
    };
    LevelPageController.prototype.start = function () {
        // 初始化时隐藏所有地图节点
        this.hideAllMapNodes();
        // 设置测试按钮点击事件
        this.setupDebugButton();
    };
    /**
     * 返回按钮点击事件
     */
    LevelPageController.prototype.onBackButtonClick = function () {
        // 如果没有有效的房间ID，说明还没有进入游戏，直接返回关卡选择页面
        if (this.currentRoomId <= 0) {
            this.returnToLevelSelect();
            return;
        }
        // 弹出确认退出对话框，type=1表示退出本局游戏，传递当前房间ID
        if (this.leaveDialogController) {
            this.leaveDialogController.show(1, function () {
            }, this.currentRoomId);
        }
        else {
            cc.warn("LeaveDialogController 未配置");
        }
    };
    /**
     * 返回到关卡选择页面
     */
    LevelPageController.prototype.returnToLevelSelect = function () {
        // 查找GlobalManagerController并切换到大厅页面
        var globalManagerNode = cc.find("Canvas/global_node") || cc.find("global_node");
        if (globalManagerNode) {
            var globalManager = globalManagerNode.getComponent(GlobalManagerController_1.default);
            if (globalManager) {
                globalManager.setCurrentPage(GlobalManagerController_1.PageType.HALL_PAGE);
            }
        }
    };
    /**
     * 开始游戏按钮点击事件
     */
    LevelPageController.prototype.onStartGameButtonClick = function () {
        // ExtendLevelInfo消息现在由LevelSelectPageController发送
        // 这里直接进入游戏，等待后端响应
    };
    /**
     * 处理ExtendLevelInfo响应
     * @param levelInfo 关卡信息响应数据
     */
    LevelPageController.prototype.onExtendLevelInfo = function (levelInfo) {
        this.currentLevelInfo = levelInfo;
        // 保存房间ID，用于退出时使用
        if (levelInfo.roomId) {
            this.currentRoomId = levelInfo.roomId;
        }
        // 重置关卡状态（包括清除测试预制体）
        this.resetLevelState();
        // 重置炸弹点击标记
        this.lastClickWasBomb = false;
        // 开始新游戏时，先重置当前棋盘（清理上一局的痕迹）
        if (this.currentSingleChessBoard) {
            this.currentSingleChessBoard.resetBoard();
        }
        // 更新地雷数UI
        this.updateMineCountUI(levelInfo.mineCount);
        // 使用当前设置的关卡编号，而不是后端返回的levelId
        // 因为后端的levelId可能与前端的关卡编号不一致
        this.enterLevel(this.currentLevel);
        // 通知当前激活的单机棋盘控制器处理ExtendLevelInfo
        if (this.currentSingleChessBoard) {
            this.currentSingleChessBoard.onExtendLevelInfo();
        }
    };
    /**
     * 更新地雷数UI
     * @param mineCount 地雷数量
     */
    LevelPageController.prototype.updateMineCountUI = function (mineCount) {
        if (this.mineCountLabel) {
            this.mineCountLabel.string = mineCount.toString();
        }
    };
    /**
     * 更新当前关卡数UI
     * @param levelNumber 关卡编号
     */
    LevelPageController.prototype.updateCurrentLevelUI = function (levelNumber) {
        if (this.currentLevelLabel) {
            this.currentLevelLabel.string = "\u7B2C" + levelNumber + "\u5173";
        }
    };
    /**
     * 根据关卡数进入相应的关卡（优化版本）
     * @param levelNumber 关卡编号
     */
    LevelPageController.prototype.enterLevel = function (levelNumber) {
        // 防止重复更新
        if (this.isUpdating) {
            return;
        }
        this.isUpdating = true;
        // 更新关卡数UI显示
        this.updateCurrentLevelUI(levelNumber);
        // 获取目标地图节点和容器
        var targetMapInfo = this.getMapNodeByLevel(levelNumber);
        if (!targetMapInfo) {
            cc.warn("\u672A\u77E5\u7684\u5173\u5361\u7F16\u53F7: " + levelNumber);
            this.isUpdating = false;
            return;
        }
        // 只有当目标节点与当前显示的节点不同时才进行切换
        if (this.lastShownMapNode !== targetMapInfo.mapNode) {
            // 隐藏上一个显示的地图节点
            if (this.lastShownMapNode) {
                this.lastShownMapNode.active = false;
            }
            // 显示目标容器和地图节点
            this.showMapContainer(targetMapInfo.containerType);
            this.showMapNodeOptimized(targetMapInfo.mapNode, targetMapInfo.mapName);
            // 记录当前显示的节点
            this.lastShownMapNode = targetMapInfo.mapNode;
        }
        // 设置当前激活的单机棋盘控制器
        if (this.singleChessBoardController) {
            // 根据关卡设置棋盘类型
            var boardType = this.getBoardTypeByLevel(levelNumber);
            this.singleChessBoardController.initBoard(boardType);
            this.currentSingleChessBoard = this.singleChessBoardController;
        }
        else {
            this.currentSingleChessBoard = null;
        }
        this.isUpdating = false;
    };
    /**
     * 根据关卡数获取对应的地图节点信息
     * @param levelNumber 关卡编号
     */
    LevelPageController.prototype.getMapNodeByLevel = function (levelNumber) {
        if (levelNumber >= 1 && levelNumber <= 4) {
            return { mapNode: this.qipan8x8Node, mapName: "qipan8*8", containerType: 'map1' };
        }
        else if (levelNumber === 5) {
            return { mapNode: this.levelS001Node, mapName: "Level_S001", containerType: 'map2' };
        }
        else if (levelNumber >= 6 && levelNumber <= 9) {
            return { mapNode: this.qipan8x9Node, mapName: "qipan8*9", containerType: 'map1' };
        }
        else if (levelNumber === 10) {
            return { mapNode: this.levelS002Node, mapName: "Level_S002", containerType: 'map2' };
        }
        else if (levelNumber >= 11 && levelNumber <= 14) {
            return { mapNode: this.qipan9x9Node, mapName: "qipan9*9", containerType: 'map1' };
        }
        else if (levelNumber === 15) {
            return { mapNode: this.levelS003Node, mapName: "Level_S003", containerType: 'map2' };
        }
        else if (levelNumber >= 16 && levelNumber <= 19) {
            return { mapNode: this.qipan9x10Node, mapName: "qipan9*10", containerType: 'map1' };
        }
        else if (levelNumber === 20) {
            return { mapNode: this.levelS004Node, mapName: "Level_S004", containerType: 'map2' };
        }
        else if (levelNumber >= 21 && levelNumber <= 24) {
            return { mapNode: this.qipan10x10Node, mapName: "qipan10*10", containerType: 'map1' };
        }
        else if (levelNumber === 25) {
            return { mapNode: this.levelS005Node, mapName: "Level_S005", containerType: 'map2' };
        }
        else if (levelNumber >= 26 && levelNumber <= 29) {
            return { mapNode: this.qipan10x10Node, mapName: "qipan10*10", containerType: 'map1' };
        }
        else if (levelNumber === 30) {
            return { mapNode: this.levelS006Node, mapName: "Level_S006", containerType: 'map2' };
        }
        return null;
    };
    /**
     * 根据关卡编号获取棋盘类型
     * @param levelNumber 关卡编号
     */
    LevelPageController.prototype.getBoardTypeByLevel = function (levelNumber) {
        if (levelNumber >= 1 && levelNumber <= 4) {
            return "8x8";
        }
        else if (levelNumber >= 6 && levelNumber <= 9) {
            return "8x9";
        }
        else if (levelNumber >= 11 && levelNumber <= 14) {
            return "9x9";
        }
        else if (levelNumber >= 16 && levelNumber <= 19) {
            return "9x10";
        }
        else if (levelNumber >= 21 && levelNumber <= 24 || levelNumber >= 26 && levelNumber <= 29) {
            return "10x10";
        }
        return "8x8"; // 默认
    };
    /**
     * 显示指定的地图节点（优化版本）
     * @param mapNode 要显示的地图节点
     * @param mapName 地图名称（用于日志）
     */
    LevelPageController.prototype.showMapNodeOptimized = function (mapNode, mapName) {
        if (mapNode) {
            mapNode.active = true;
        }
        else {
            cc.warn("\u274C \u5730\u56FE\u8282\u70B9\u672A\u627E\u5230: " + mapName);
        }
    };
    /**
     * 显示指定的地图容器
     * @param containerType 容器类型
     */
    LevelPageController.prototype.showMapContainer = function (containerType) {
        // 确保 level_page 节点是激活的
        if (this.levelPageNode) {
            this.levelPageNode.active = true;
        }
        // 根据容器类型显示对应容器，隐藏另一个
        if (containerType === 'map1') {
            if (this.gameMap1Node && !this.gameMap1Node.active) {
                this.gameMap1Node.active = true;
            }
            if (this.gameMap2Node && this.gameMap2Node.active) {
                this.gameMap2Node.active = false;
            }
        }
        else {
            if (this.gameMap2Node && !this.gameMap2Node.active) {
                this.gameMap2Node.active = true;
            }
            if (this.gameMap1Node && this.gameMap1Node.active) {
                this.gameMap1Node.active = false;
            }
        }
    };
    /**
     * 隐藏所有地图节点
     */
    LevelPageController.prototype.hideAllMapNodes = function () {
        var allMapNodes = [
            this.qipan8x8Node,
            this.qipan8x9Node,
            this.qipan9x9Node,
            this.qipan9x10Node,
            this.qipan10x10Node,
            this.levelS001Node,
            this.levelS002Node,
            this.levelS003Node,
            this.levelS004Node,
            this.levelS005Node,
            this.levelS006Node
        ];
        allMapNodes.forEach(function (node) {
            if (node) {
                node.active = false;
            }
        });
    };
    /**
     * 设置当前关卡（从外部调用）
     * @param levelNumber 关卡编号
     */
    LevelPageController.prototype.setCurrentLevel = function (levelNumber) {
        // 重置关卡状态（包括清除测试预制体）
        this.resetLevelState();
        this.currentLevel = levelNumber;
        // 立即根据关卡数切换地图显示
        this.enterLevel(levelNumber);
    };
    /**
     * 获取当前关卡编号
     */
    LevelPageController.prototype.getCurrentLevel = function () {
        return this.currentLevel;
    };
    /**
     * 获取当前关卡信息
     */
    LevelPageController.prototype.getCurrentLevelInfo = function () {
        return this.currentLevelInfo;
    };
    /**
     * 获取当前房间ID
     */
    LevelPageController.prototype.getCurrentRoomId = function () {
        return this.currentRoomId;
    };
    /**
     * 隐藏所有地图容器
     */
    LevelPageController.prototype.hideAllMapContainers = function () {
        // 确保 level_page 节点是激活的
        if (this.levelPageNode) {
            this.levelPageNode.active = true;
        }
        // 隐藏两个主要的地图容器
        if (this.gameMap1Node) {
            this.gameMap1Node.active = false;
        }
        if (this.gameMap2Node) {
            this.gameMap2Node.active = false;
        }
        // 同时隐藏所有具体的地图节点
        this.hideAllMapNodes();
    };
    /**
     * 显示 game_map_1 容器（方形地图）
     */
    LevelPageController.prototype.showGameMap1 = function () {
        if (this.gameMap1Node) {
            this.gameMap1Node.active = true;
        }
        else {
            cc.warn("❌ game_map_1 节点未找到");
        }
    };
    /**
     * 显示 game_map_2 容器（特殊关卡）
     */
    LevelPageController.prototype.showGameMap2 = function () {
        if (this.gameMap2Node) {
            this.gameMap2Node.active = true;
        }
        else {
            cc.warn("❌ game_map_2 节点未找到");
        }
    };
    /**
     * 获取当前激活的单机棋盘控制器
     */
    LevelPageController.prototype.getCurrentSingleChessBoard = function () {
        return this.currentSingleChessBoard;
    };
    /**
     * 处理单机模式的点击响应
     * @param response 点击响应数据
     */
    LevelPageController.prototype.handleSingleModeClickResponse = function (response) {
        if (this.currentSingleChessBoard) {
            var x = response.x, y = response.y, result = response.result, chainReaction = response.chainReaction;
            // 处理点击结果
            if (x !== undefined && y !== undefined && result !== undefined) {
                this.currentSingleChessBoard.handleClickResponse(x, y, result);
            }
            // 处理连锁反应
            if (chainReaction && Array.isArray(chainReaction)) {
                this.currentSingleChessBoard.handleChainReaction(chainReaction);
            }
        }
    };
    /**
     * 处理单机模式游戏结束
     * @param gameEndData 游戏结束数据
     */
    LevelPageController.prototype.handleSingleModeGameEnd = function (gameEndData) {
        if (this.currentSingleChessBoard) {
            // 禁用棋盘触摸
            this.currentSingleChessBoard.disableAllGridTouch();
            // 处理游戏结束
            this.currentSingleChessBoard.onLevelGameEnd();
        }
    };
    /**
     * 重置当前单机棋盘（仅在开始新游戏时调用）
     */
    LevelPageController.prototype.resetCurrentSingleChessBoard = function () {
        if (this.currentSingleChessBoard) {
            // 重置棋盘状态（清理所有预制体和格子状态）
            this.currentSingleChessBoard.resetBoard();
            // 重新启用触摸事件
            this.currentSingleChessBoard.enableAllGridTouch();
        }
    };
    /**
     * 注册单机模式消息处理器
     */
    LevelPageController.prototype.registerSingleModeMessageHandlers = function () {
        // 监听WebSocket消息
        GameMgr_1.GameMgr.Event.AddEventListener(EventCenter_1.EventType.ReceiveMessage, this.onReceiveMessage, this);
    };
    /**
     * 取消单机模式消息监听
     */
    LevelPageController.prototype.unregisterSingleModeMessageHandlers = function () {
        GameMgr_1.GameMgr.Event.RemoveEventListener(EventCenter_1.EventType.ReceiveMessage, this.onReceiveMessage, this);
    };
    /**
     * 处理接收到的WebSocket消息
     * @param messageBean 消息数据
     */
    LevelPageController.prototype.onReceiveMessage = function (messageBean) {
        switch (messageBean.msgId) {
            case MessageId_1.MessageId.MsgTypeLevelClickBlock:
                this.onLevelClickBlockResponse(messageBean.data);
                break;
            case MessageId_1.MessageId.MsgTypeLevelGameEnd:
                this.onLevelGameEnd(messageBean.data);
                break;
        }
    };
    /**
     * 处理LevelClickBlock响应
     * @param response 点击响应数据
     */
    LevelPageController.prototype.onLevelClickBlockResponse = function (response) {
        if (this.currentSingleChessBoard) {
            // 解构响应数据，支持多种可能的字段名
            var x = response.x, y = response.y, result = response.result, action = response.action, chainReaction = response.chainReaction, revealedGrids = response.revealedGrids, floodFill = response.floodFill, revealedBlocks = response.revealedBlocks, floodFillResults = response.floodFillResults // 单机模式使用这个字段
            ;
            // 根据action类型处理不同的响应
            if (x !== undefined && y !== undefined && result !== undefined) {
                if (action === 2) {
                    // 标记/取消标记操作，不调用handleClickResponse，避免格子消失
                    // 不调用 handleClickResponse，因为标记操作不应该隐藏格子
                }
                else if (action === 1) {
                    // 挖掘操作
                    console.log("\uD83C\uDFAF \u70B9\u51FB\u54CD\u5E94: (" + x + ", " + y + "), \u7ED3\u679C: " + result);
                    // 检查是否点到炸弹
                    if (result === "boom" || result === "mine") {
                        console.log("💣 检测到炸弹点击，设置 lastClickWasBomb = true");
                        this.lastClickWasBomb = true;
                    }
                    this.currentSingleChessBoard.handleClickResponse(x, y, result);
                    // 处理连锁展开数据
                    if (floodFillResults && Array.isArray(floodFillResults) && floodFillResults.length > 0) {
                        this.currentSingleChessBoard.handleFloodFillResults(floodFillResults);
                    }
                }
                else {
                    // 其他操作，默认按挖掘处理
                    this.currentSingleChessBoard.handleClickResponse(x, y, result);
                }
            }
        }
    };
    /**
     * 处理LevelGameEnd通知
     * @param gameEndData 游戏结束数据
     */
    LevelPageController.prototype.onLevelGameEnd = function (gameEndData) {
        var _this = this;
        if (this.currentSingleChessBoard) {
            // 禁用棋盘触摸
            this.currentSingleChessBoard.disableAllGridTouch();
            // 处理游戏结束（不清理数据）
            this.currentSingleChessBoard.onLevelGameEnd();
        }
        // 检查是否点到了炸弹，如果是游戏失败且点到炸弹，则延迟显示结算页面
        var isGameFailed = !gameEndData.success;
        var hasBombExploded = this.lastClickWasBomb ||
            (this.currentSingleChessBoard && this.currentSingleChessBoard.hasBombExplodedInThisGame());
        console.log("=== 游戏结束延迟检查 ===");
        console.log("gameEndData:", gameEndData);
        console.log("isGameFailed:", isGameFailed);
        console.log("lastClickWasBomb:", this.lastClickWasBomb);
        console.log("currentSingleChessBoard:", !!this.currentSingleChessBoard);
        console.log("hasBombExploded:", hasBombExploded);
        if (isGameFailed && hasBombExploded) {
            // 点到炸弹导致的游戏失败，延迟1.5秒显示结算页面
            console.log("✅ 满足延迟条件：点到炸弹导致游戏失败，延迟1.5秒显示结算页面");
            this.scheduleOnce(function () {
                console.log("⏰ 延迟时间到，现在显示结算页面");
                _this.showLevelSettlement(gameEndData);
                // 重置标记
                _this.lastClickWasBomb = false;
            }, 1.5);
        }
        else {
            // 其他情况，立即显示结算页面
            console.log("❌ 不满足延迟条件，立即显示结算页面");
            this.showLevelSettlement(gameEndData);
            // 重置标记
            this.lastClickWasBomb = false;
        }
    };
    /**
     * 设置结算页面按钮事件
     */
    LevelPageController.prototype.setupSettlementButtons = function () {
        var _this = this;
        // 再来一次按钮
        if (this.retryButton) {
            this.retryButton.node.on('click', this.onRetryButtonClick, this);
        }
        // 下一关按钮
        if (this.nextLevelButton) {
            this.nextLevelButton.node.on('click', this.onNextLevelButtonClick, this);
        }
        // 退出按钮 - 使用按压效果，模仿其他返回按钮
        if (this.exitButton) {
            Tools_1.Tools.imageButtonClick(this.exitButton.node, Config_1.Config.buttonRes + 'board_btn_back_normal', Config_1.Config.buttonRes + 'board_btn_back_pressed', function () {
                _this.onExitButtonClick();
            });
        }
    };
    /**
     * 显示结算页面
     * @param gameEndData 游戏结束数据
     */
    LevelPageController.prototype.showLevelSettlement = function (gameEndData) {
        console.log("🎯 showLevelSettlement 被调用");
        console.log("gameEndData:", gameEndData);
        if (!this.levelSettlementNode) {
            console.error("levelSettlementNode 未配置");
            return;
        }
        // 显示结算页面
        console.log("📱 设置 levelSettlementNode.active = true");
        this.levelSettlementNode.active = true;
        // 根据游戏结果显示对应的背景 - 修复成功判断逻辑
        var isSuccess = gameEndData.isSuccess || gameEndData.success || gameEndData.isWin || gameEndData.gameStatus === 1;
        if (isSuccess) {
            // 成功 - 显示胜利背景
            if (this.winBgNode) {
                this.winBgNode.active = true;
            }
            if (this.loseBgNode) {
                this.loseBgNode.active = false;
            }
        }
        else {
            // 失败 - 显示失败背景
            if (this.loseBgNode) {
                this.loseBgNode.active = true;
            }
            if (this.winBgNode) {
                this.winBgNode.active = false;
            }
        }
    };
    /**
     * 再来一次按钮点击事件
     */
    LevelPageController.prototype.onRetryButtonClick = function () {
        // 关闭结算页面
        this.hideLevelSettlement();
        // 注意：不在这里重置棋盘，等收到ExtendLevelInfo响应时再重置
        // 这样可以避免过早清理，让玩家在点击按钮后还能短暂看到游玩痕迹
        // 发送当前关卡的ExtendLevelInfo
        this.sendExtendLevelInfo(this.currentLevel);
    };
    /**
     * 下一关按钮点击事件
     */
    LevelPageController.prototype.onNextLevelButtonClick = function () {
        // 关闭结算页面
        this.hideLevelSettlement();
        // 进入下一关
        var nextLevel = this.currentLevel + 1;
        this.setCurrentLevel(nextLevel);
        // 注意：不在这里重置棋盘，等收到ExtendLevelInfo响应时再重置
        // 这样可以避免过早清理，让玩家在点击按钮后还能短暂看到游玩痕迹
        // 发送下一关的ExtendLevelInfo
        this.sendExtendLevelInfo(nextLevel);
    };
    /**
     * 退出按钮点击事件
     */
    LevelPageController.prototype.onExitButtonClick = function () {
        console.log("退出按钮被点击");
        // 关闭结算页面
        this.hideLevelSettlement();
        // 返回到关卡选择页面（匹配界面）
        this.returnToLevelSelect();
    };
    /**
     * 隐藏结算页面
     */
    LevelPageController.prototype.hideLevelSettlement = function () {
        if (this.levelSettlementNode) {
            this.levelSettlementNode.active = false;
        }
    };
    /**
     * 发送ExtendLevelInfo消息
     * @param levelId 关卡ID
     */
    LevelPageController.prototype.sendExtendLevelInfo = function (levelId) {
        var request = {
            levelId: levelId
        };
        WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeExtendLevelInfo, request);
    };
    /**
     * 设置测试按钮
     */
    LevelPageController.prototype.setupDebugButton = function () {
        if (this.debugShowMinesButton) {
            this.debugShowMinesButton.node.on('click', this.onDebugShowMinesClick, this);
        }
    };
    /**
     * 测试按钮点击事件 - 发送DebugShowMines消息
     */
    LevelPageController.prototype.onDebugShowMinesClick = function () {
        WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeDebugShowMines, {});
    };
    /**
     * 判断是否在单机模式
     */
    LevelPageController.prototype.isInSingleMode = function () {
        // 单机模式的判断逻辑：当前页面是关卡页面且有有效的房间ID
        return this.currentRoomId > 0;
    };
    /**
     * 处理DebugShowMines响应，在炸弹位置生成测试预制体
     * @param minePositions 炸弹位置数组 [{x: number, y: number}]
     */
    LevelPageController.prototype.handleDebugShowMines = function (minePositions) {
        var _this = this;
        if (!this.debugMinePrefab) {
            cc.warn("debugMinePrefab 预制体未设置，无法显示测试标记");
            return;
        }
        if (!this.currentSingleChessBoard) {
            cc.warn("当前没有激活的单机棋盘");
            return;
        }
        if (!minePositions || !Array.isArray(minePositions) || minePositions.length === 0) {
            cc.warn("地雷位置数据无效:", minePositions);
            return;
        }
        // 先尝试直接创建一个测试预制体，不使用延迟
        if (minePositions.length > 0) {
            var firstPosition = minePositions[0];
            // 直接调用，不使用延迟
            this.createDebugMinePrefab(firstPosition.x, firstPosition.y);
        }
        // 在每个炸弹位置生成测试预制体
        minePositions.forEach(function (position, index) {
            if (index === 0) {
                // 第一个不延迟，立即执行
                _this.createDebugMinePrefab(position.x, position.y);
            }
            else {
                // 其他的使用延迟
                _this.scheduleOnce(function () {
                    _this.createDebugMinePrefab(position.x, position.y);
                }, index * 0.1);
            }
        });
    };
    /**
     * 在指定位置创建测试预制体
     * @param x 格子x坐标
     * @param y 格子y坐标
     */
    LevelPageController.prototype.createDebugMinePrefab = function (x, y) {
        if (!this.debugMinePrefab) {
            cc.error("debugMinePrefab 为空，无法创建测试预制体");
            return;
        }
        if (!this.currentSingleChessBoard) {
            cc.error("currentSingleChessBoard 为空，无法创建测试预制体");
            return;
        }
        try {
            // 使用棋盘控制器的公共方法创建自定义预制体
            var debugNode = this.currentSingleChessBoard.createCustomPrefab(x, y, this.debugMinePrefab, "DebugMine_" + x + "_" + y);
            if (debugNode) {
                // 将创建的节点存储起来，用于后续清理
                this.debugMineNodes.push(debugNode);
            }
            else {
                cc.error("\u274C \u5728\u4F4D\u7F6E (" + x + ", " + y + ") \u521B\u5EFA\u6D4B\u8BD5\u9884\u5236\u4F53\u5931\u8D25\uFF0C\u8FD4\u56DE\u503C\u4E3A\u7A7A");
            }
        }
        catch (error) {
            cc.error("\u274C \u521B\u5EFA\u6D4B\u8BD5\u9884\u5236\u4F53\u65F6\u53D1\u751F\u9519\u8BEF:", error);
        }
    };
    /**
     * 清除所有测试预制体
     */
    LevelPageController.prototype.clearDebugMines = function () {
        this.debugMineNodes.forEach(function (node, index) {
            if (node && cc.isValid(node)) {
                cc.log("\u6E05\u9664\u6D4B\u8BD5\u9884\u5236\u4F53 " + (index + 1) + ":", node.name);
                node.destroy();
            }
        });
        // 清空数组
        this.debugMineNodes = [];
    };
    /**
     * 重置关卡状态（包括清除测试预制体）
     */
    LevelPageController.prototype.resetLevelState = function () {
        this.clearDebugMines();
        // 这里可以添加其他需要重置的状态
    };
    LevelPageController.prototype.onDestroy = function () {
        // 取消消息监听
        this.unregisterSingleModeMessageHandlers();
        // 清理测试预制体
        this.clearDebugMines();
        // 清理按钮事件
        if (this.retryButton) {
            this.retryButton.node.off('click', this.onRetryButtonClick, this);
        }
        if (this.nextLevelButton) {
            this.nextLevelButton.node.off('click', this.onNextLevelButtonClick, this);
        }
        // 退出按钮使用 Tools.imageButtonClick，会自动管理事件，无需手动清理
        if (this.debugShowMinesButton) {
            this.debugShowMinesButton.node.off('click', this.onDebugShowMinesClick, this);
        }
    };
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "backButton", void 0);
    __decorate([
        property(cc.Button)
    ], LevelPageController.prototype, "startGameButton", void 0);
    __decorate([
        property(cc.Label)
    ], LevelPageController.prototype, "mineCountLabel", void 0);
    __decorate([
        property(cc.Label)
    ], LevelPageController.prototype, "currentLevelLabel", void 0);
    __decorate([
        property(LeaveDialogController_1.default)
    ], LevelPageController.prototype, "leaveDialogController", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelPageNode", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "gameMap1Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "gameMap2Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "qipan8x8Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "qipan8x9Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "qipan9x9Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "qipan9x10Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "qipan10x10Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelS001Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelS002Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelS003Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelS004Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelS005Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelS006Node", void 0);
    __decorate([
        property(SingleChessBoardController_1.default)
    ], LevelPageController.prototype, "singleChessBoardController", void 0);
    __decorate([
        property(cc.Button)
    ], LevelPageController.prototype, "debugShowMinesButton", void 0);
    __decorate([
        property(cc.Prefab)
    ], LevelPageController.prototype, "debugMinePrefab", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelSettlementNode", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "boardBgNode", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "loseBgNode", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "winBgNode", void 0);
    __decorate([
        property(cc.Button)
    ], LevelPageController.prototype, "retryButton", void 0);
    __decorate([
        property(cc.Button)
    ], LevelPageController.prototype, "nextLevelButton", void 0);
    __decorate([
        property(cc.Button)
    ], LevelPageController.prototype, "exitButton", void 0);
    LevelPageController = __decorate([
        ccclass
    ], LevelPageController);
    return LevelPageController;
}(cc.Component));
exports.default = LevelPageController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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