
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/game/Chess/ChessBoardController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'b2da8U/JnpOW6usOBaTL1QA', 'ChessBoardController');
// scripts/game/Chess/ChessBoardController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __spreadArrays = (this && this.__spreadArrays) || function () {
    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;
    for (var r = Array(s), k = 0, i = 0; i < il; i++)
        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)
            r[k] = a[j];
    return r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var GlobalBean_1 = require("../../bean/GlobalBean");
var PlayerGameController_1 = require("../../pfb/PlayerGameController ");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var ChessBoardController = /** @class */ (function (_super) {
    __extends(ChessBoardController, _super);
    function ChessBoardController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.playerGamePrefab = null;
        _this.boomPrefab = null;
        _this.biaojiPrefab = null;
        _this.boom1Prefab = null;
        _this.boom2Prefab = null;
        _this.boom3Prefab = null;
        _this.boom4Prefab = null;
        _this.boom5Prefab = null;
        _this.boom6Prefab = null;
        _this.boom7Prefab = null;
        _this.boom8Prefab = null; // player_game_pfb 预制体
        _this.boardNode = null; // 棋盘节点
        // 棋盘配置
        _this.BOARD_SIZE = 8; // 8x8棋盘
        _this.BOARD_WIDTH = 750; // 棋盘总宽度
        _this.BOARD_HEIGHT = 750; // 棋盘总高度
        _this.GRID_SIZE = 88; // 每个格子的大小 88x88
        // 格子数据存储
        _this.gridData = []; // 二维数组存储格子数据
        _this.gridNodes = []; // 二维数组存储格子节点
        // 添加到坐标历史记录
        _this.coordinateHistory = [];
        // 自定义偏移量（如果需要调整位置）
        _this.customOffsetX = 0;
        _this.customOffsetY = -16; // 恢复原来的值，保持点击生成位置正确
        return _this;
        // update (dt) {}
    }
    ChessBoardController.prototype.onLoad = function () {
        this.initBoard();
    };
    ChessBoardController.prototype.start = function () {
        var _this = this;
        // 延迟一帧后再次尝试启用触摸事件，确保所有节点都已创建完成
        this.scheduleOnce(function () {
            _this.enableTouchForExistingGrids();
        }, 0.1);
    };
    // 初始化棋盘
    ChessBoardController.prototype.initBoard = function () {
        // 初始化数据数组
        for (var x = 0; x < this.BOARD_SIZE; x++) {
            this.gridData[x] = [];
            this.gridNodes[x] = [];
            for (var y = 0; y < this.BOARD_SIZE; y++) {
                this.gridData[x][y] = {
                    x: x,
                    y: y,
                    worldPos: this.getGridWorldPosition(x, y),
                    hasPlayer: false
                };
            }
        }
        this.createGridNodes();
    };
    // 启用现有格子的触摸事件
    ChessBoardController.prototype.createGridNodes = function () {
        if (!this.boardNode) {
            console.error("棋盘节点未设置！");
            return;
        }
        // 如果格子已经存在，直接启用触摸事件
        this.enableTouchForExistingGrids();
    };
    // 为现有格子启用触摸事件
    ChessBoardController.prototype.enableTouchForExistingGrids = function () {
        // 检查棋盘节点是否存在
        if (!this.boardNode) {
            console.error("棋盘节点未设置，无法启用触摸事件！");
            return;
        }
        // 遍历棋盘节点的所有子节点
        var children = this.boardNode.children;
        for (var i = 0; i < children.length; i++) {
            var child = children[i];
            // 尝试从节点名称解析坐标
            var coords = this.parseGridCoordinateFromName(child.name);
            if (coords) {
                this.setupGridTouchEvents(child, coords.x, coords.y);
                this.gridNodes[coords.x] = this.gridNodes[coords.x] || [];
                this.gridNodes[coords.x][coords.y] = child;
            }
            else {
                // 如果无法从名称解析，尝试从位置计算
                var pos = child.getPosition();
                var coords_1 = this.getGridCoordinateFromPosition(pos);
                if (coords_1) {
                    this.setupGridTouchEvents(child, coords_1.x, coords_1.y);
                    this.gridNodes[coords_1.x] = this.gridNodes[coords_1.x] || [];
                    this.gridNodes[coords_1.x][coords_1.y] = child;
                }
            }
        }
    };
    // 从节点名称解析格子坐标
    ChessBoardController.prototype.parseGridCoordinateFromName = function (nodeName) {
        // 尝试匹配 Grid_x_y 格式
        var match = nodeName.match(/Grid_(\d+)_(\d+)/);
        if (match) {
            return { x: parseInt(match[1]), y: parseInt(match[2]) };
        }
        return null;
    };
    // 从位置计算格子坐标
    ChessBoardController.prototype.getGridCoordinateFromPosition = function (pos) {
        var x = Math.floor((pos.x + this.BOARD_WIDTH / 2) / this.GRID_SIZE);
        var y = Math.floor((pos.y + this.BOARD_HEIGHT / 2) / this.GRID_SIZE);
        if (this.isValidCoordinate(x, y)) {
            return { x: x, y: y };
        }
        return null;
    };
    // 为格子节点设置触摸事件
    ChessBoardController.prototype.setupGridTouchEvents = function (gridNode, x, y) {
        var _this = this;
        // 安全检查：确保坐标有效
        if (!this.isValidCoordinate(x, y)) {
            console.error("\u274C setupGridTouchEvents: \u5C1D\u8BD5\u4E3A\u65E0\u6548\u5750\u6807(" + x + "," + y + ")\u8BBE\u7F6E\u89E6\u6478\u4E8B\u4EF6");
            return;
        }
        // 长按相关变量
        var isLongPressing = false;
        var longPressTimer = 0;
        var longPressCallback = null;
        var LONG_PRESS_TIME = 1.0; // 1秒长按时间
        // 触摸开始事件
        gridNode.on(cc.Node.EventType.TOUCH_START, function (_event) {
            isLongPressing = true;
            longPressTimer = 0;
            // 开始长按检测
            longPressCallback = function () {
                if (isLongPressing) {
                    longPressTimer += 0.1;
                    if (longPressTimer >= LONG_PRESS_TIME) {
                        _this.onGridLongPress(x, y);
                        isLongPressing = false;
                        if (longPressCallback) {
                            _this.unschedule(longPressCallback);
                        }
                    }
                }
            };
            _this.schedule(longPressCallback, 0.1);
        }, this);
        // 触摸结束事件
        gridNode.on(cc.Node.EventType.TOUCH_END, function (event) {
            // 如果不是长按，则执行点击事件
            if (isLongPressing && longPressTimer < LONG_PRESS_TIME) {
                _this.onGridClick(x, y, event);
            }
            isLongPressing = false;
            if (longPressCallback) {
                _this.unschedule(longPressCallback);
            }
        }, this);
        // 触摸取消事件
        gridNode.on(cc.Node.EventType.TOUCH_CANCEL, function (_event) {
            isLongPressing = false;
            if (longPressCallback) {
                _this.unschedule(longPressCallback);
            }
        }, this);
        // 添加Button组件以确保触摸响应
        var button = gridNode.getComponent(cc.Button);
        if (!button) {
            button = gridNode.addComponent(cc.Button);
            button.transition = cc.Button.Transition.SCALE;
            button.zoomScale = 0.95;
        }
    };
    // 计算格子的世界坐标位置（左下角为(0,0)）
    ChessBoardController.prototype.getGridWorldPosition = function (x, y) {
        // 计算格子中心点位置
        // 左下角为(0,0)，所以y坐标需要从下往上计算
        var posX = (x * this.GRID_SIZE) + (this.GRID_SIZE / 2) - (this.BOARD_WIDTH / 2);
        var posY = (y * this.GRID_SIZE) + (this.GRID_SIZE / 2) - (this.BOARD_HEIGHT / 2);
        return cc.v2(posX, posY);
    };
    // 格子点击事件 - 发送挖掘操作
    ChessBoardController.prototype.onGridClick = function (x, y, _event) {
        // 检查坐标是否有效（确保在8x8棋盘范围内）
        if (!this.isValidCoordinate(x, y)) {
            return;
        }
        // 检查该位置是否已经有玩家预制体
        if (this.gridData[x][y].hasPlayer) {
            return;
        }
        // 发送挖掘操作事件 (action = 1)
        // 只发送事件，不直接生成预制体，等待GamePageController确认后再生成
        this.node.emit('chess-board-click', {
            x: x,
            y: y,
            action: 1 // 1 = 挖掘
        });
    };
    // 格子长按事件 - 发送标记操作
    ChessBoardController.prototype.onGridLongPress = function (x, y) {
        // 检查坐标是否有效（确保在8x8棋盘范围内）
        if (!this.isValidCoordinate(x, y)) {
            return;
        }
        // 检查该位置是否已经有玩家预制体
        if (this.gridData[x][y].hasPlayer) {
            return;
        }
        // 发送标记操作事件 (action = 2)
        // 只发送事件，不直接生成预制体，等待GamePageController确认后再生成
        this.node.emit('chess-board-click', {
            x: x,
            y: y,
            action: 2 // 2 = 标记
        });
    };
    // 在格子上放置玩家预制体
    ChessBoardController.prototype.placePlayerOnGrid = function (x, y, withFlag) {
        var _this = this;
        if (withFlag === void 0) { withFlag = false; }
        // 双重检查：确保坐标有效
        if (!this.isValidCoordinate(x, y)) {
            console.error("\u274C placePlayerOnGrid: \u65E0\u6548\u5750\u6807(" + x + "," + y + ")");
            return;
        }
        // 双重检查：确保格子为空
        var gridData = this.gridData[x][y];
        if (gridData.hasPlayer) {
            console.error("\u274C placePlayerOnGrid: \u683C\u5B50(" + x + "," + y + ")\u5DF2\u6709\u73A9\u5BB6\uFF0C\u4E0D\u80FD\u91CD\u590D\u653E\u7F6E");
            return;
        }
        if (!this.playerGamePrefab) {
            console.error("❌ 玩家预制体未设置！");
            return;
        }
        if (!this.boardNode) {
            console.error("❌ 棋盘节点未设置！");
            return;
        }
        // 实例化玩家预制体
        var playerNode = cc.instantiate(this.playerGamePrefab);
        // 计算正确的位置
        var correctPosition = this.calculateCorrectPosition(x, y);
        playerNode.setPosition(correctPosition);
        // 先隐藏节点，等头像加载完成后再显示
        playerNode.active = false;
        // 处理Layout限制问题
        this.addPlayerNodeSafely(playerNode);
        // 设置头像和用户数据（异步加载）
        this.setupPlayerAvatarAsync(playerNode, x, y, withFlag, function () {
            // 头像加载完成的回调，播放生成动画（点击生成和单人格子）
            _this.playAvatarSpawnAnimation(playerNode);
        });
        // 更新格子数据
        gridData.hasPlayer = true;
        gridData.playerNode = playerNode;
    };
    // 计算正确的位置（格子中心偏移(0, -16)）
    ChessBoardController.prototype.calculateCorrectPosition = function (x, y) {
        // 使用自定义偏移量
        var offsetX = this.customOffsetX;
        var offsetY = this.customOffsetY;
        // 方法1: 如果能找到对应的格子节点，使用其位置并添加偏移
        var targetGridNode = this.gridNodes[x] && this.gridNodes[x][y];
        if (targetGridNode) {
            var gridPos = targetGridNode.getPosition();
            var finalPos = cc.v2(gridPos.x + offsetX, gridPos.y + offsetY);
            return finalPos;
        }
        // 方法2: 基于棋盘的实际位置计算
        // 计算格子中心位置
        var centerX = (x * this.GRID_SIZE) + (this.GRID_SIZE / 2) - (this.BOARD_WIDTH / 2);
        var centerY = (y * this.GRID_SIZE) + (this.GRID_SIZE / 2) - (this.BOARD_HEIGHT / 2);
        // 添加偏移
        var finalX = centerX + offsetX;
        var finalY = centerY + offsetY;
        var calculatedPos = cc.v2(finalX, finalY);
        return calculatedPos;
    };
    /**
     * 计算预制体的精确位置（根据您提供的坐标规律）
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @returns 预制体应该放置的精确位置
     */
    ChessBoardController.prototype.calculatePrefabPosition = function (x, y) {
        // 根据您提供的坐标规律计算：
        // (0,0) → (-314, -310)
        // (1,0) → (-224, -310)  // x增加90
        // (0,1) → (-314, -222)  // y增加88
        // (7,7) → (310, 312)
        var startX = -314; // 起始X坐标
        var startY = -310; // 起始Y坐标
        var stepX = 90; // X方向步长
        var stepY = 88; // Y方向步长
        var finalX = startX + (x * stepX);
        var finalY = startY + (y * stepY);
        var position = cc.v2(finalX, finalY);
        return position;
    };
    /**
     * 播放头像生成动画（由大变小）
     * @param playerNode 玩家节点
     */
    ChessBoardController.prototype.playAvatarSpawnAnimation = function (playerNode) {
        if (!playerNode) {
            console.warn("播放生成动画失败：节点为空");
            return;
        }
        // 显示节点
        playerNode.active = true;
        // 设置初始缩放为1.5倍（比正常大）
        var originalScale = playerNode.scaleX;
        var startScale = originalScale * 1.5;
        playerNode.setScale(startScale);
        // 使用cc.Tween创建由大变小的缩放动画
        cc.tween(playerNode)
            .to(0.3, { scaleX: originalScale, scaleY: originalScale }, { easing: 'backOut' })
            .start();
    };
    /**
     * 播放头像调整动画（平滑移动和缩小）
     * @param playerNode 玩家节点
     * @param newPosition 新位置
     * @param newScale 新缩放
     */
    ChessBoardController.prototype.playAvatarAdjustAnimation = function (playerNode, newPosition, newScale) {
        if (!playerNode) {
            console.warn("播放调整动画失败：节点为空");
            return;
        }
        // 使用cc.Tween同时播放移动和缩放动画
        cc.tween(playerNode)
            .to(0.3, {
            x: newPosition.x,
            y: newPosition.y,
            scaleX: newScale,
            scaleY: newScale
        }, { easing: 'sineOut' })
            .start();
    };
    /**
     * 根据格子总人数计算基础位置（统一逻辑）
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param totalPlayers 该格子的总人数
     * @returns 基础位置
     */
    ChessBoardController.prototype.calculateBasePositionByPlayerCount = function (x, y, totalPlayers) {
        var offsetX = this.customOffsetX;
        var offsetY;
        if (totalPlayers === 1) {
            // 一个格子里只有一个人：需要偏移
            offsetY = this.customOffsetY; // -16
        }
        else {
            // 一个格子里有两个及以上：不偏移
            offsetY = 0;
        }
        // 方法1: 如果能找到对应的格子节点，使用其位置并添加偏移
        var targetGridNode = this.gridNodes[x] && this.gridNodes[x][y];
        if (targetGridNode) {
            var gridPos = targetGridNode.getPosition();
            var finalPos = cc.v2(gridPos.x + offsetX, gridPos.y + offsetY);
            return finalPos;
        }
        // 方法2: 基于棋盘的实际位置计算
        var centerX = (x * this.GRID_SIZE) + (this.GRID_SIZE / 2) - (this.BOARD_WIDTH / 2);
        var centerY = (y * this.GRID_SIZE) + (this.GRID_SIZE / 2) - (this.BOARD_HEIGHT / 2);
        var finalX = centerX + offsetX;
        var finalY = centerY + offsetY;
        var calculatedPos = cc.v2(finalX, finalY);
        return calculatedPos;
    };
    /**
     * 计算多人情况下的基础位置（不包含往下偏移，逻辑分开）
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @returns 格子中心位置（多人专用，不偏移）
     */
    ChessBoardController.prototype.calculateMultiPlayerBasePosition = function (x, y) {
        // 多人情况使用独立的偏移逻辑
        var offsetX = this.customOffsetX;
        var offsetY = 0; // 多人时不往下偏移，逻辑分开
        // 方法1: 如果能找到对应的格子节点，使用其位置并添加偏移
        var targetGridNode = this.gridNodes[x] && this.gridNodes[x][y];
        if (targetGridNode) {
            var gridPos = targetGridNode.getPosition();
            var finalPos = cc.v2(gridPos.x + offsetX, gridPos.y + offsetY);
            return finalPos;
        }
        // 方法2: 基于棋盘的实际位置计算
        // 计算格子中心位置
        var centerX = (x * this.GRID_SIZE) + (this.GRID_SIZE / 2) - (this.BOARD_WIDTH / 2);
        var centerY = (y * this.GRID_SIZE) + (this.GRID_SIZE / 2) - (this.BOARD_HEIGHT / 2);
        // 添加偏移（不包含往下偏移）
        var finalX = centerX + offsetX;
        var finalY = centerY + offsetY;
        var calculatedPos = cc.v2(finalX, finalY);
        return calculatedPos;
    };
    // 安全地添加玩家节点（处理Layout限制）
    ChessBoardController.prototype.addPlayerNodeSafely = function (playerNode) {
        // 检查棋盘节点是否存在
        if (!this.boardNode) {
            console.error("棋盘节点未设置，无法添加玩家节点！");
            return;
        }
        // 检查棋盘节点是否有Layout组件
        var layout = this.boardNode.getComponent(cc.Layout);
        if (layout) {
            // 方案1: 临时禁用Layout
            layout.enabled = false;
            // 添加节点
            this.boardNode.addChild(playerNode);
        }
        else {
            this.boardNode.addChild(playerNode);
        }
        // 方案2备选：添加到Layout外部
        // this.addToParentNode(playerNode);
    };
    // 备选方案：添加到父节点（Layout外部）
    ChessBoardController.prototype.addToParentNode = function (playerNode) {
        // 检查棋盘节点是否存在
        if (!this.boardNode) {
            console.error("棋盘节点未设置，无法添加玩家节点！");
            return;
        }
        if (this.boardNode.parent) {
            // 需要转换坐标系
            var worldPos = this.boardNode.convertToWorldSpaceAR(playerNode.getPosition());
            var localPos = this.boardNode.parent.convertToNodeSpaceAR(worldPos);
            playerNode.setPosition(localPos);
            this.boardNode.parent.addChild(playerNode);
        }
        else {
            console.error("\u274C \u68CB\u76D8\u8282\u70B9\u6CA1\u6709\u7236\u8282\u70B9");
            // 回退到直接添加
            this.boardNode.addChild(playerNode);
        }
    };
    // 异步设置玩家头像（带回调）
    ChessBoardController.prototype.setupPlayerAvatarAsync = function (playerNode, x, y, withFlag, onComplete) {
        // 查找PlayerGameController组件
        var playerController = playerNode.getComponent("PlayerGameController") ||
            playerNode.getComponent("PlayerGameController ") ||
            playerNode.getComponentInChildren("PlayerGameController");
        if (playerController) {
            // 检查avatar节点是否存在
            if (playerController.avatar) {
                // 检查avatar节点是否有Sprite组件
                var avatarSprite = playerController.avatar.getComponent(cc.Sprite);
                if (!avatarSprite) {
                    avatarSprite = playerController.avatar.addComponent(cc.Sprite);
                }
                // 确保avatar节点可见
                playerController.avatar.active = true;
                playerController.avatar.opacity = 255;
            }
            else {
                console.error("❌ PlayerGameController中的avatar节点为null");
                onComplete();
                return;
            }
            // 设置旗子节点的显示状态 - 重点检查
            if (playerController.flagNode) {
                playerController.flagNode.active = withFlag;
                // 额外检查旗子节点的可见性
                if (withFlag) {
                    playerController.flagNode.opacity = 255;
                    // 确保旗子节点的父节点也是可见的
                    var parent = playerController.flagNode.parent;
                    while (parent && parent !== playerNode) {
                        parent.active = true;
                        parent = parent.parent;
                    }
                    // 延迟检查旗子是否真的显示了
                    this.scheduleOnce(function () {
                    }, 1.0);
                }
            }
            else {
                console.warn("\u26A0\uFE0F \u627E\u4E0D\u5230\u65D7\u5B50\u8282\u70B9 (" + x + "," + y + ")");
            }
            // 创建用户数据并设置头像
            var userData = {
                userId: "player_" + x + "_" + y,
                nickName: "\u73A9\u5BB6(" + x + "," + y + ")",
                avatar: this.getDefaultAvatarUrl(),
                score: 0,
                pos: 0,
                coin: 0,
                status: 0,
                rank: 0
            };
            // 使用PlayerGameController的setData方法来设置头像
            try {
                playerController.setData(userData);
                // 延迟设置旗子状态，确保在PlayerGameController初始化之后
                this.scheduleOnce(function () {
                    if (playerController.flagNode) {
                        playerController.flagNode.active = withFlag;
                    }
                    onComplete();
                }, 0.1);
            }
            catch (error) {
                console.error("设置头像数据失败:", error);
                onComplete();
            }
        }
        else {
            console.warn("⚠️ 找不到PlayerGameController组件");
            this.tryDirectAvatarSetupAsync(playerNode, x, y, onComplete);
        }
    };
    // 设置玩家头像（保留原方法用于其他地方）
    ChessBoardController.prototype.setupPlayerAvatar = function (playerNode, x, y) {
        var _this = this;
        // 查找PlayerGameController组件
        var playerController = playerNode.getComponent("PlayerGameController") ||
            playerNode.getComponent("PlayerGameController ") ||
            playerNode.getComponentInChildren("PlayerGameController");
        if (playerController) {
            // 检查avatar节点是否存在
            if (playerController.avatar) {
                // 检查avatar节点是否有Sprite组件
                var avatarSprite = playerController.avatar.getComponent(cc.Sprite);
                if (!avatarSprite) {
                    console.warn("⚠️ avatar节点缺少Sprite组件，正在添加...");
                    avatarSprite = playerController.avatar.addComponent(cc.Sprite);
                }
                // 确保avatar节点可见
                playerController.avatar.active = true;
                playerController.avatar.opacity = 255;
            }
            else {
                console.error("❌ PlayerGameController中的avatar节点为null");
                return;
            }
            // 创建用户数据
            var userData = {
                userId: "player_" + x + "_" + y,
                nickName: "\u73A9\u5BB6(" + x + "," + y + ")",
                avatar: this.getDefaultAvatarUrl(),
                score: 0,
                pos: 0,
                coin: 0,
                status: 0,
                rank: 0
            };
            // 安全地调用setData
            try {
                playerController.setData(userData);
                // 延迟检查头像是否加载成功
                this.scheduleOnce(function () {
                    _this.checkAvatarLoaded(playerController.avatar, x, y);
                }, 2.0);
            }
            catch (error) {
                console.error("❌ 设置头像时出错:", error);
            }
        }
        else {
            console.warn("⚠️ 找不到PlayerGameController组件，跳过头像设置");
            // 尝试直接在节点上查找avatar子节点
            this.tryDirectAvatarSetup(playerNode, x, y);
        }
    };
    // 检查头像是否加载成功
    ChessBoardController.prototype.checkAvatarLoaded = function (avatarNode, x, y) {
        if (!avatarNode) {
            console.error("\u274C \u4F4D\u7F6E(" + x + "," + y + ")\u7684avatar\u8282\u70B9\u4E3Anull");
            return;
        }
        var sprite = avatarNode.getComponent(cc.Sprite);
        if (!sprite) {
            console.error("\u274C \u4F4D\u7F6E(" + x + "," + y + ")\u7684avatar\u8282\u70B9\u6CA1\u6709Sprite\u7EC4\u4EF6");
            return;
        }
        if (!sprite.spriteFrame) {
            console.warn("\u26A0\uFE0F \u4F4D\u7F6E(" + x + "," + y + ")\u7684\u5934\u50CF\u53EF\u80FD\u52A0\u8F7D\u5931\u8D25\uFF0CspriteFrame\u4E3Anull");
            // 尝试设置一个默认的颜色作为备用显示
            this.setFallbackAvatar(avatarNode, x, y);
        }
        else {
        }
    };
    // 设置备用头像（纯色方块）
    ChessBoardController.prototype.setFallbackAvatar = function (avatarNode, x, y) {
        var sprite = avatarNode.getComponent(cc.Sprite);
        if (!sprite) {
            sprite = avatarNode.addComponent(cc.Sprite);
        }
        // 创建一个简单的纯色纹理
        var texture = new cc.Texture2D();
        var colors = [
            [255, 107, 107, 255],
            [78, 205, 196, 255],
            [69, 183, 209, 255],
            [150, 206, 180, 255],
            [255, 234, 167, 255] // 黄色
        ];
        var colorIndex = (x + y) % colors.length;
        var color = colors[colorIndex];
        texture.initWithData(new Uint8Array(color), cc.Texture2D.PixelFormat.RGBA8888, 1, 1);
        sprite.spriteFrame = new cc.SpriteFrame(texture);
        // 设置大小
        avatarNode.setContentSize(80, 80);
        avatarNode.active = true;
    };
    // 尝试直接设置头像（当找不到PlayerGameController时）
    ChessBoardController.prototype.tryDirectAvatarSetup = function (playerNode, x, y) {
        // 查找名为"avatar"的子节点
        var avatarNode = playerNode.getChildByName("avatar");
        if (avatarNode) {
            this.setFallbackAvatar(avatarNode, x, y);
        }
        else {
            console.warn("⚠️ 未找到avatar子节点");
            // 列出所有子节点名称
        }
    };
    // 获取默认头像URL
    ChessBoardController.prototype.getDefaultAvatarUrl = function () {
        // 使用真实的头像URL
        return "https://static.gooplay.com/online/user-avatar/1732786296530322669.jpg";
    };
    // 保存格子坐标（用于后续发送给后端）
    ChessBoardController.prototype.saveGridCoordinate = function (x, y) {
        // 这里可以将坐标保存到数组或发送给后端
        // 示例：可以调用网络管理器发送坐标
        this.sendCoordinateToServer(x, y);
        // 或者保存到本地数组以备后用
        this.addToCoordinateHistory(x, y);
    };
    // 发送坐标到服务器
    ChessBoardController.prototype.sendCoordinateToServer = function (x, y) {
        // 构造发送数据
        var moveData = {
            x: x,
            y: y,
            timestamp: Date.now(),
            playerId: this.getCurrentPlayerId()
        };
        // 暂时只是打印，避免未使用变量警告
        return moveData;
    };
    ChessBoardController.prototype.addToCoordinateHistory = function (x, y) {
        this.coordinateHistory.push({
            x: x,
            y: y,
            timestamp: Date.now()
        });
    };
    // 获取当前玩家ID（示例）
    ChessBoardController.prototype.getCurrentPlayerId = function () {
        // 这里应该从全局状态或用户数据中获取
        return "player_001"; // 示例ID
    };
    // 获取指定坐标的格子数据
    ChessBoardController.prototype.getGridData = function (x, y) {
        if (x < 0 || x >= this.BOARD_SIZE || y < 0 || y >= this.BOARD_SIZE) {
            return null;
        }
        return this.gridData[x][y];
    };
    // 清除指定格子的玩家
    ChessBoardController.prototype.clearGridPlayer = function (x, y) {
        var gridData = this.getGridData(x, y);
        if (!gridData || !gridData.hasPlayer) {
            return false;
        }
        // 移除玩家节点
        if (gridData.playerNode) {
            gridData.playerNode.removeFromParent();
            gridData.playerNode = null;
        }
        // 更新数据
        gridData.hasPlayer = false;
        return true;
    };
    // 清除所有玩家
    ChessBoardController.prototype.clearAllPlayers = function () {
        for (var x = 0; x < this.BOARD_SIZE; x++) {
            for (var y = 0; y < this.BOARD_SIZE; y++) {
                this.clearGridPlayer(x, y);
            }
        }
    };
    // 获取所有已放置玩家的坐标
    ChessBoardController.prototype.getAllPlayerCoordinates = function () {
        var coordinates = [];
        for (var x = 0; x < this.BOARD_SIZE; x++) {
            for (var y = 0; y < this.BOARD_SIZE; y++) {
                if (this.gridData[x][y].hasPlayer) {
                    coordinates.push({ x: x, y: y });
                }
            }
        }
        return coordinates;
    };
    // 检查坐标是否有效
    ChessBoardController.prototype.isValidCoordinate = function (x, y) {
        return x >= 0 && x < this.BOARD_SIZE && y >= 0 && y < this.BOARD_SIZE;
    };
    // 检查格子是否为空
    ChessBoardController.prototype.isGridEmpty = function (x, y) {
        if (!this.isValidCoordinate(x, y)) {
            return false;
        }
        return !this.gridData[x][y].hasPlayer;
    };
    // 获取坐标历史记录
    ChessBoardController.prototype.getCoordinateHistory = function () {
        return __spreadArrays(this.coordinateHistory); // 返回副本
    };
    // 清除坐标历史记录
    ChessBoardController.prototype.clearCoordinateHistory = function () {
        this.coordinateHistory = [];
    };
    // 根据世界坐标获取格子坐标
    ChessBoardController.prototype.getGridCoordinateFromWorldPos = function (worldPos) {
        // 检查棋盘节点是否存在
        if (!this.boardNode) {
            console.error("棋盘节点未设置，无法获取格子坐标！");
            return null;
        }
        // 将世界坐标转换为相对于棋盘的坐标
        var localPos = this.boardNode.convertToNodeSpaceAR(worldPos);
        // 计算格子坐标
        var x = Math.floor((localPos.x + this.BOARD_WIDTH / 2) / this.GRID_SIZE);
        var y = Math.floor((localPos.y + this.BOARD_HEIGHT / 2) / this.GRID_SIZE);
        if (this.isValidCoordinate(x, y)) {
            return { x: x, y: y };
        }
        return null;
    };
    // 高亮显示格子（可选功能）
    ChessBoardController.prototype.highlightGrid = function (x, y, highlight) {
        if (highlight === void 0) { highlight = true; }
        if (!this.isValidCoordinate(x, y)) {
            return;
        }
        var gridNode = this.gridNodes[x][y];
        if (gridNode) {
            // 这里可以添加高亮效果，比如改变颜色或添加边框
            if (highlight) {
                gridNode.color = cc.Color.YELLOW;
            }
            else {
                gridNode.color = cc.Color.WHITE;
            }
        }
    };
    // 批量放置玩家（用于从服务器同步数据）
    ChessBoardController.prototype.batchPlacePlayers = function (coordinates) {
        var _this = this;
        coordinates.forEach(function (coord) {
            if (_this.isValidCoordinate(coord.x, coord.y) && _this.isGridEmpty(coord.x, coord.y)) {
                _this.placePlayerOnGrid(coord.x, coord.y);
            }
        });
    };
    // 手动启用触摸事件（调试用）
    ChessBoardController.prototype.manualEnableTouch = function () {
        this.enableTouchForExistingGrids();
    };
    // 测试点击功能（调试用）
    ChessBoardController.prototype.testClick = function (x, y) {
        this.onGridClick(x, y);
    };
    // 获取棋盘状态信息（调试用）
    ChessBoardController.prototype.getBoardInfo = function () {
        var info = {
            boardSize: this.BOARD_SIZE,
            gridSize: this.GRID_SIZE,
            boardWidth: this.BOARD_WIDTH,
            boardHeight: this.BOARD_HEIGHT,
            totalGrids: this.BOARD_SIZE * this.BOARD_SIZE,
            boardNodeChildren: this.boardNode ? this.boardNode.children.length : 0,
            playerCount: this.getAllPlayerCoordinates().length,
            hasPlayerGamePrefab: !!this.playerGamePrefab,
            hasBoardNode: !!this.boardNode
        };
        return info;
    };
    // 简单测试方法 - 只测试位置，不加载头像
    ChessBoardController.prototype.simpleTest = function (x, y) {
        if (!this.boardNode) {
            console.error("❌ 棋盘节点未设置");
            return;
        }
        // 创建一个简单的彩色方块
        var testNode = new cc.Node("Test_" + x + "_" + y);
        // 添加一个彩色方块
        var sprite = testNode.addComponent(cc.Sprite);
        var texture = new cc.Texture2D();
        var color = [Math.random() * 255, Math.random() * 255, Math.random() * 255, 255];
        texture.initWithData(new Uint8Array(color), cc.Texture2D.PixelFormat.RGBA8888, 1, 1);
        sprite.spriteFrame = new cc.SpriteFrame(texture);
        // 设置大小
        testNode.setContentSize(60, 60);
        // 计算位置
        var pos = this.calculateCorrectPosition(x, y);
        testNode.setPosition(pos);
        // 添加坐标标签
        var labelNode = new cc.Node("Label");
        var label = labelNode.addComponent(cc.Label);
        label.string = "(" + x + "," + y + ")";
        label.fontSize = 16;
        label.node.color = cc.Color.WHITE;
        labelNode.setPosition(0, 0);
        testNode.addChild(labelNode);
        // 添加到棋盘（处理Layout问题）
        this.addPlayerNodeSafely(testNode);
    };
    // 清除所有测试节点
    ChessBoardController.prototype.clearTestNodes = function () {
        if (this.boardNode) {
            var children = this.boardNode.children.slice();
            children.forEach(function (child) {
                if (child.name.startsWith("Test_")) {
                    child.removeFromParent();
                }
            });
        }
    };
    // 切换到父节点添加模式（如果Layout问题仍然存在）
    ChessBoardController.prototype.useParentNodeMode = function () {
        // 重新定义添加方法
        this.addPlayerNodeSafely = this.addToParentNode;
    };
    // 重新启用Layout（如果需要）
    ChessBoardController.prototype.reEnableLayout = function () {
        if (!this.boardNode) {
            console.error("棋盘节点未设置，无法重新启用Layout！");
            return;
        }
        var layout = this.boardNode.getComponent(cc.Layout);
        if (layout) {
            layout.enabled = true;
        }
    };
    // 永久禁用Layout
    ChessBoardController.prototype.disableLayout = function () {
        if (!this.boardNode) {
            console.error("棋盘节点未设置，无法禁用Layout！");
            return;
        }
        var layout = this.boardNode.getComponent(cc.Layout);
        if (layout) {
            layout.enabled = false;
        }
    };
    // 设置自定义偏移量
    ChessBoardController.prototype.setCustomOffset = function (offsetX, offsetY) {
        this.customOffsetX = offsetX;
        this.customOffsetY = offsetY;
    };
    // 获取当前偏移量
    ChessBoardController.prototype.getCurrentOffset = function () {
        return { x: this.customOffsetX, y: this.customOffsetY };
    };
    // 测试不同偏移量
    ChessBoardController.prototype.testWithOffset = function (x, y, offsetX, offsetY) {
        // 临时保存当前偏移
        var originalOffsetX = this.customOffsetX;
        var originalOffsetY = this.customOffsetY;
        // 设置测试偏移
        this.setCustomOffset(offsetX, offsetY);
        // 执行测试
        this.simpleTest(x, y);
        // 恢复原偏移
        this.setCustomOffset(originalOffsetX, originalOffsetY);
    };
    // 测试头像显示功能
    ChessBoardController.prototype.testAvatarDisplay = function (x, y) {
        var _this = this;
        if (!this.isValidCoordinate(x, y)) {
            console.error("❌ 无效坐标");
            return;
        }
        if (this.gridData[x][y].hasPlayer) {
            console.warn("⚠️ 该位置已有玩家");
            return;
        }
        // 直接调用放置玩家方法
        this.placePlayerOnGrid(x, y);
        // 延迟检查结果
        this.scheduleOnce(function () {
            var gridData = _this.gridData[x][y];
            if (gridData.playerNode) {
                // 检查PlayerGameController
                var controller = gridData.playerNode.getComponent("PlayerGameController");
                if (controller && controller.avatar) {
                    var sprite = controller.avatar.getComponent(cc.Sprite);
                    if (sprite && sprite.spriteFrame) {
                    }
                    else {
                        console.warn("⚠️ 头像SpriteFrame不存在");
                    }
                }
                else {
                    console.warn("⚠️ PlayerGameController或avatar节点不存在");
                }
            }
            else {
                console.error("❌ 玩家节点创建失败");
            }
        }, 3.0);
    };
    // 调试预制体结构
    ChessBoardController.prototype.debugPrefabStructure = function () {
        if (!this.playerGamePrefab) {
            console.error("❌ playerGamePrefab为null");
            return;
        }
        // 实例化一个临时节点来检查结构
        var tempNode = cc.instantiate(this.playerGamePrefab);
        // 检查组件
        var controller = tempNode.getComponent("PlayerGameController");
        if (controller) {
            if (controller.avatar) {
                var sprite = controller.avatar.getComponent(cc.Sprite);
            }
            else {
                console.error("❌ avatar节点不存在");
            }
        }
        else {
            console.error("❌ 找不到PlayerGameController组件");
        }
        // 列出所有子节点
        this.logNodeHierarchy(tempNode, 0);
        // 清理临时节点
        tempNode.destroy();
    };
    // 递归打印节点层级
    ChessBoardController.prototype.logNodeHierarchy = function (node, depth) {
        var indent = "  ".repeat(depth);
        for (var _i = 0, _a = node.children; _i < _a.length; _i++) {
            var child = _a[_i];
            this.logNodeHierarchy(child, depth + 1);
        }
    };
    // 异步加载头像
    ChessBoardController.prototype.loadAvatarAsync = function (avatarNode, url, onComplete) {
        var _this = this;
        if (!avatarNode) {
            console.error("❌ avatar节点为null");
            onComplete();
            return;
        }
        var avatarSprite = avatarNode.getComponent(cc.Sprite);
        if (!avatarSprite) {
            console.warn("⚠️ avatar节点没有Sprite组件，正在添加...");
            avatarSprite = avatarNode.addComponent(cc.Sprite);
        }
        if (!url || url === '') {
            console.warn("⚠️ URL为空，设置备用头像");
            this.setFallbackAvatar(avatarNode, 0, 0);
            onComplete();
            return;
        }
        // 根据URL判断文件扩展名
        var ext = '.png';
        if (url.toLowerCase().includes('.jpg') || url.toLowerCase().includes('.jpeg')) {
            ext = '.jpg';
        }
        else if (url.toLowerCase().includes('.png')) {
            ext = '.png';
        }
        cc.assetManager.loadRemote(url, { ext: ext }, function (err, texture) {
            if (err) {
                console.error("\u274C \u5934\u50CF\u52A0\u8F7D\u5931\u8D25: " + (err.message || err));
                console.error("\u274C \u5931\u8D25\u7684URL: " + url);
                // 设置备用头像
                _this.setFallbackAvatar(avatarNode, 0, 0);
                onComplete();
                return;
            }
            texture.setPremultiplyAlpha(true);
            texture.packable = false;
            avatarSprite.spriteFrame = new cc.SpriteFrame(texture);
            // 确保节点可见
            avatarNode.active = true;
            avatarNode.opacity = 255;
            onComplete();
        });
    };
    // 异步直接设置头像（当找不到PlayerGameController时）
    ChessBoardController.prototype.tryDirectAvatarSetupAsync = function (playerNode, x, y, onComplete) {
        // 查找名为"avatar"的子节点
        var avatarNode = playerNode.getChildByName("avatar");
        if (avatarNode) {
            this.setFallbackAvatar(avatarNode, x, y);
            onComplete();
        }
        else {
            console.warn("⚠️ 未找到avatar子节点");
            // 列出所有子节点名称
            onComplete();
        }
    };
    /**
     * 显示玩家游戏加减分效果
     * @param userId 用户ID
     * @param score 分数变化（正数为加分，负数为减分）
     */
    ChessBoardController.prototype.showPlayerGameScore = function (userId, score) {
        var currentUserId = this.getCurrentUserId();
        var foundPlayer = false;
        // 1. 如果是当前用户，查找自己的玩家节点（存储在gridData中）
        if (userId === currentUserId) {
            foundPlayer = this.showScoreForCurrentUser(score);
        }
        else {
            // 2. 如果是其他用户，查找对应的玩家头像节点
            foundPlayer = this.showScoreForOtherUser(userId, score);
        }
        if (!foundPlayer) {
            console.warn("\u672A\u627E\u5230\u7528\u6237 " + userId + " \u7684\u5934\u50CF\u8282\u70B9\u6765\u663E\u793A\u5206\u6570\u6548\u679C");
        }
    };
    /**
     * 获取当前用户ID
     */
    ChessBoardController.prototype.getCurrentUserId = function () {
        var _a, _b;
        return ((_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId) || "";
    };
    /**
     * 为当前用户显示分数效果
     */
    ChessBoardController.prototype.showScoreForCurrentUser = function (score) {
        for (var x = 0; x < this.BOARD_SIZE; x++) {
            for (var y = 0; y < this.BOARD_SIZE; y++) {
                var gridData = this.gridData[x][y];
                if (gridData.hasPlayer && gridData.playerNode) {
                    var playerController = gridData.playerNode.getComponent("PlayerGameController") ||
                        gridData.playerNode.getComponent("PlayerGameController ");
                    if (playerController) {
                        this.showScoreOnPlayerController(playerController, score);
                        return true;
                    }
                }
            }
        }
        return false;
    };
    /**
     * 为其他用户显示分数效果
     */
    ChessBoardController.prototype.showScoreForOtherUser = function (userId, score) {
        if (!this.boardNode) {
            return false;
        }
        // 遍历棋盘上的所有玩家头像节点
        // 由于目前没有在节点上存储userId，我们需要通过其他方式匹配
        // 临时方案：根据最近的操作位置来匹配
        return this.findPlayerNodeByRecentAction(userId, score);
    };
    /**
     * 根据userId查找对应的玩家节点
     */
    ChessBoardController.prototype.findPlayerNodeByRecentAction = function (userId, score) {
        if (!this.boardNode) {
            console.warn("\u68CB\u76D8\u8282\u70B9\u4E0D\u5B58\u5728\uFF0C\u65E0\u6CD5\u67E5\u627E\u7528\u6237 " + userId + " \u7684\u5934\u50CF");
            return false;
        }
        // 遍历棋盘上的所有玩家头像节点，根据存储的userId精确匹配
        for (var i = 0; i < this.boardNode.children.length; i++) {
            var child = this.boardNode.children[i];
            // 尝试多种方式获取PlayerGameController组件
            var playerController = child.getComponent("PlayerGameController");
            if (!playerController) {
                playerController = child.getComponent("PlayerGameController "); // 注意末尾有空格
            }
            if (!playerController) {
                // 尝试通过类名获取
                var components = child.getComponents(cc.Component);
                playerController = components.find(function (comp) {
                    return comp.constructor.name === 'PlayerGameController' ||
                        comp.constructor.name === 'PlayerGameController ';
                });
            }
            var storedUserId = child['userId'];
            // 先输出组件列表，帮助诊断问题
            if (storedUserId && (storedUserId === userId || i < 5)) { // 为前5个节点或匹配的节点输出组件列表
                var allComponents = child.getComponents(cc.Component);
            }
            if (storedUserId === userId) {
                if (playerController) {
                    // 找到匹配的用户ID和组件，显示分数效果
                    this.showScoreOnPlayerController(playerController, score);
                    return true;
                }
                else {
                    // 找到匹配的用户ID但没有组件
                    console.warn("\u26A0\uFE0F \u627E\u5230\u7528\u6237 " + userId + " \u7684\u8282\u70B9\u4F46\u6CA1\u6709PlayerGameController\u7EC4\u4EF6");
                    return false; // 找到节点但没有组件，返回false
                }
            }
        }
        console.warn("\u274C \u672A\u627E\u5230\u7528\u6237 " + userId + " \u7684\u5934\u50CF\u8282\u70B9");
        return false;
    };
    /**
     * 在PlayerController上显示分数效果
     */
    ChessBoardController.prototype.showScoreOnPlayerController = function (playerController, score) {
        // 临时提升节点层级，避免被其他头像遮挡
        var playerNode = playerController.node;
        var originalSiblingIndex = playerNode.getSiblingIndex();
        // 将节点移到最上层
        playerNode.setSiblingIndex(-1);
        // 同时确保加分/减分节点的层级更高
        this.ensureScoreNodeTopLevel(playerController);
        if (score > 0) {
            playerController.showAddScore(score);
        }
        else if (score < 0) {
            playerController.showSubScore(Math.abs(score));
        }
        // 延迟恢复原始层级（等分数动画播放完成）
        this.scheduleOnce(function () {
            if (playerNode && playerNode.isValid) {
                playerNode.setSiblingIndex(originalSiblingIndex);
            }
        }, 2.5); // 增加到2.5秒，确保动画完全结束
    };
    /**
     * 确保加分/减分节点在最高层级
     */
    ChessBoardController.prototype.ensureScoreNodeTopLevel = function (playerController) {
        // 设置加分节点的最高层级
        if (playerController.addScoreNode) {
            playerController.addScoreNode.zIndex = cc.macro.MAX_ZINDEX - 1;
        }
        // 设置减分节点的最高层级
        if (playerController.subScoreNode) {
            playerController.subScoreNode.zIndex = cc.macro.MAX_ZINDEX - 1;
        }
    };
    /**
     * 显示玩家游戏减分效果
     * @param userId 用户ID
     * @param subScore 减分数值
     */
    ChessBoardController.prototype.showPlayerGameSubScore = function (userId, subScore) {
        var foundPlayer = false;
        // 遍历所有格子，查找玩家节点
        for (var x = 0; x < this.BOARD_SIZE; x++) {
            for (var y = 0; y < this.BOARD_SIZE; y++) {
                var gridData = this.gridData[x][y];
                if (gridData.hasPlayer && gridData.playerNode) {
                    var playerController = gridData.playerNode.getComponent("PlayerGameController") ||
                        gridData.playerNode.getComponent("PlayerGameController ");
                    if (playerController) {
                        playerController.showSubScore(subScore);
                        foundPlayer = true;
                        break;
                    }
                }
            }
            if (foundPlayer)
                break;
        }
        if (!foundPlayer) {
            console.warn("\u672A\u627E\u5230\u73A9\u5BB6\u8282\u70B9\u6765\u663E\u793A\u51CF\u5206\u6548\u679C: userId=" + userId);
        }
    };
    /**
     * 重置游戏场景（游戏开始时调用）
     * 清除数字、炸弹、标记预制体，重新显示所有小格子
     */
    ChessBoardController.prototype.resetGameScene = function () {
        if (!this.boardNode) {
            console.error("❌ 棋盘节点不存在，无法重置");
            return;
        }
        // 列出所有子节点
        for (var i = 0; i < this.boardNode.children.length; i++) {
            var child = this.boardNode.children[i];
        }
        // 清除所有游戏元素（数字、炸弹、标记等）
        this.clearAllGameElements();
        // 显示所有小格子
        this.showAllGrids();
        // 重新初始化棋盘数据
        this.reinitializeBoardData();
        // 列出所有子节点
        for (var i = 0; i < this.boardNode.children.length; i++) {
            var child = this.boardNode.children[i];
        }
    };
    /**
     * 测试重置功能（可以在浏览器控制台手动调用）
     */
    ChessBoardController.prototype.testReset = function () {
        this.resetGameScene();
    };
    /**
     * 清除所有游戏元素（数字、炸弹、标记、玩家头像等），但保留小格子
     */
    ChessBoardController.prototype.clearAllGameElements = function () {
        if (!this.boardNode) {
            return;
        }
        var childrenToRemove = [];
        var totalChildren = this.boardNode.children.length;
        // 遍历棋盘的所有子节点
        for (var i = 0; i < this.boardNode.children.length; i++) {
            var child = this.boardNode.children[i];
            var nodeName = child.name;
            // 检查是否是需要清除的游戏元素（不包括小格子）
            if (this.isGameElement(child, nodeName)) {
                childrenToRemove.push(child);
            }
            else {
            }
        }
        // 移除找到的游戏元素
        childrenToRemove.forEach(function (child, index) {
            child.removeFromParent();
        });
        // 暂时禁用强制清理，避免误删小格子
        // this.forceCleanNonGridNodes();
    };
    /**
     * 强制清理所有游戏预制体（除了Grid_开头的节点和分数控制器）
     */
    ChessBoardController.prototype.forceCleanNonGridNodes = function () {
        if (!this.boardNode) {
            return;
        }
        var childrenToRemove = [];
        // 再次遍历，强制清除所有游戏预制体
        for (var i = 0; i < this.boardNode.children.length; i++) {
            var child = this.boardNode.children[i];
            var nodeName = child.name;
            // 保留条件：
            // 1. Grid_开头的节点（小格子）
            // 2. 包含Score的节点（分数控制器）
            // 3. UI相关节点
            var shouldKeep = nodeName.startsWith("Grid_") ||
                nodeName.includes("Score") ||
                nodeName.includes("score") ||
                nodeName.includes("UI") ||
                nodeName.includes("ui") ||
                nodeName.includes("Canvas") ||
                nodeName.includes("Background");
            if (!shouldKeep) {
                childrenToRemove.push(child);
            }
            // 移除找到的节点
            childrenToRemove.forEach(function (child) {
                child.removeFromParent();
            });
        }
    };
    /**
     * 判断节点是否是游戏元素（需要清除的），小格子和分数控制器不会被清除
     */
    ChessBoardController.prototype.isGameElement = function (node, nodeName) {
        //  绝对不清除的节点（小格子）
        if (nodeName.startsWith("Grid_") || nodeName === "block") {
            return false;
        }
        //  分数控制器不清除
        if (nodeName.includes("Score") || nodeName.includes("score")) {
            return false;
        }
        //  UI相关节点不清除
        if (nodeName.includes("UI") || nodeName.includes("ui")) {
            return false;
        }
        // 🗑️明确需要清除的游戏预制体
        // 炸弹预制体
        if (nodeName === "Boom") {
            return true;
        }
        // 数字预制体（Boom1, Boom2, Boom3 等）
        if (nodeName.match(/^Boom\d+$/)) {
            return true;
        }
        // 临时数字节点（NeighborMines_1, NeighborMines_2 等）
        if (nodeName.match(/^NeighborMines_\d+$/)) {
            return true;
        }
        // 测试节点（Test_x_y 格式）
        if (nodeName.match(/^Test_\d+_\d+$/)) {
            return true;
        }
        // 玩家预制体（通过组件判断）
        if (node.getComponent("PlayerGameController")) {
            return true;
        }
        // 标记预制体
        if (nodeName.includes("Flag") || nodeName.includes("Mark") || nodeName.includes("flag") ||
            nodeName === "Biaoji" || nodeName.includes("Biaoji")) {
            return true;
        }
        // 玩家头像预制体
        if (nodeName.includes("Player") || nodeName.includes("Avatar")) {
            return true;
        }
        //  默认保留未知节点（保守策略）
        return false;
    };
    /**
     * 显示所有小格子（第二把游戏开始时恢复被隐藏的小格子）
     */
    ChessBoardController.prototype.showAllGrids = function () {
        if (!this.boardNode) {
            return;
        }
        var totalGrids = 0;
        var restoredGrids = 0;
        var alreadyVisibleGrids = 0;
        // 遍历棋盘的所有子节点，找到小格子并显示
        for (var i = 0; i < this.boardNode.children.length; i++) {
            var child = this.boardNode.children[i];
            // 如果是小格子节点
            if (child.name.startsWith("Grid_") || child.name === "block") {
                totalGrids++;
                // 记录恢复前的状态
                var wasHidden = !child.active || child.opacity < 255 || child.scaleX < 1 || child.scaleY < 1;
                if (wasHidden) {
                    restoredGrids++;
                }
                else {
                    alreadyVisibleGrids++;
                }
                // 停止所有可能正在进行的动画
                child.stopAllActions();
                // 恢复显示状态
                child.active = true;
                child.opacity = 255;
                child.scaleX = 1;
                child.scaleY = 1;
                child.angle = 0; // 重置旋转角度
                // 恢复原始位置（如果有保存的话）
                if (child['originalPosition']) {
                    child.setPosition(child['originalPosition']);
                }
                // 确保格子可以交互
                var button = child.getComponent(cc.Button);
                if (button) {
                    button.enabled = true;
                }
            }
        }
    };
    /**
     * 隐藏指定位置的小格子（点击时调用）
     */
    ChessBoardController.prototype.hideGridAt = function (x, y) {
        if (!this.isValidCoordinate(x, y)) {
            console.warn("\u9690\u85CF\u683C\u5B50\u5931\u8D25\uFF1A\u5750\u6807(" + x + ", " + y + ")\u65E0\u6548");
            return;
        }
        // 获取格子节点
        var gridNode = this.gridNodes[x] && this.gridNodes[x][y];
        if (gridNode) {
            // 使用动画隐藏格子
            cc.tween(gridNode)
                .to(0.3, { opacity: 0, scaleX: 0, scaleY: 0 }, { easing: 'sineIn' })
                .call(function () {
                gridNode.active = false;
            })
                .start();
        }
    };
    /**
     * 重新初始化棋盘数据
     */
    ChessBoardController.prototype.reinitializeBoardData = function () {
        // 重置gridData中的玩家状态
        for (var x = 0; x < this.BOARD_SIZE; x++) {
            for (var y = 0; y < this.BOARD_SIZE; y++) {
                if (this.gridData[x][y]) {
                    this.gridData[x][y].hasPlayer = false;
                    this.gridData[x][y].playerNode = null;
                }
            }
        }
        // 清除坐标历史记录
        this.clearCoordinateHistory();
    };
    /**
     * 清理所有玩家预制体（新回合开始时调用）
     * 包括自己的头像和其他玩家的头像
     */
    ChessBoardController.prototype.clearAllPlayerNodes = function () {
        if (!this.boardNode) {
            console.warn("棋盘节点不存在，无法清理");
            return;
        }
        var totalCleared = 0;
        // 方法1: 清理存储在gridData中的玩家节点（自己的头像）
        for (var x = 0; x < this.BOARD_SIZE; x++) {
            for (var y = 0; y < this.BOARD_SIZE; y++) {
                if (this.gridData[x][y].hasPlayer && this.gridData[x][y].playerNode) {
                    // 移除玩家节点
                    this.gridData[x][y].playerNode.removeFromParent();
                    this.gridData[x][y].playerNode = null;
                    this.gridData[x][y].hasPlayer = false;
                    totalCleared++;
                }
            }
        }
        // 方法2: 清理棋盘上所有的玩家预制体节点（包括其他玩家的头像）
        var childrenToRemove = [];
        // 遍历棋盘的所有子节点
        for (var i = 0; i < this.boardNode.children.length; i++) {
            var child = this.boardNode.children[i];
            // 检查是否是玩家预制体（通过组件判断）
            var playerController = child.getComponent(PlayerGameController_1.default);
            if (playerController) {
                childrenToRemove.push(child);
                totalCleared++;
            }
        }
        // 移除找到的玩家预制体
        childrenToRemove.forEach(function (child) {
            child.removeFromParent();
        });
    };
    /**
     * 在指定位置显示其他玩家的操作（参考自己头像的生成逻辑）
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param actions 该位置的其他玩家操作列表
     */
    ChessBoardController.prototype.displayOtherPlayersAtPosition = function (x, y, actions) {
        if (!this.isValidCoordinate(x, y) || !actions || actions.length === 0) {
            console.warn("\u65E0\u6548\u53C2\u6570: (" + x + ", " + y + "), actions: " + ((actions === null || actions === void 0 ? void 0 : actions.length) || 0));
            return;
        }
        // 检查该位置是否已经有自己的头像
        if (this.gridData[x][y].hasPlayer) {
            // 只有当真的有其他玩家时，才调整自己的头像位置
            if (actions.length > 0) {
                // 如果已有自己的头像且有其他玩家，需要使用多人布局策略
                this.addOtherPlayersToExistingGrid(x, y, actions);
            }
            else {
            }
        }
        else {
            // 如果没有自己的头像，直接添加其他玩家头像
            this.addOtherPlayersToEmptyGrid(x, y, actions);
        }
    };
    /**
     * 在已有自己头像的格子上添加其他玩家头像，并调整自己的头像位置和缩放
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param actions 其他玩家操作列表
     */
    ChessBoardController.prototype.addOtherPlayersToExistingGrid = function (x, y, actions) {
        // 总玩家数 = 自己(1) + 其他玩家数量
        var totalPlayers = 1 + actions.length;
        var positions = this.getPlayerPositions(totalPlayers);
        // 第一步：调整自己的头像位置和缩放
        // 注意：如果自己的头像是通过点击生成的，位置是正确的，应该调整
        // 如果是通过后端消息生成的，也应该参与多人布局
        var myPosition = positions[0]; // 第一个位置是自己的
        this.adjustMyAvatarPosition(x, y, myPosition, actions);
        // 第二步：从第二个位置开始放置其他玩家
        for (var i = 0; i < actions.length; i++) {
            var action = actions[i];
            var position = positions[i + 1]; // 跳过第一个位置（自己的位置）
            // 使用棋盘坐标系创建其他玩家头像
            this.createOtherPlayerAtBoardPosition(x, y, action, position, totalPlayers);
        }
    };
    /**
     * 调整自己的头像位置和缩放（当多人在同一格子时）
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param position 新的位置和缩放信息
     * @param actions 其他玩家操作列表
     */
    ChessBoardController.prototype.adjustMyAvatarPosition = function (x, y, position, actions) {
        // 查找自己的头像节点
        if (!this.gridData[x][y].hasPlayer || !this.gridData[x][y].playerNode) {
            console.warn("\u5728\u4F4D\u7F6E(" + x + ", " + y + ")\u627E\u4E0D\u5230\u81EA\u5DF1\u7684\u5934\u50CF\u8282\u70B9");
            return;
        }
        var myPlayerNode = this.gridData[x][y].playerNode;
        // 计算该格子的总人数（自己 + 其他玩家）
        var totalPlayers = 1 + (actions ? actions.length : 0);
        // 根据总人数计算基础位置
        var basePosition = this.calculateBasePositionByPlayerCount(x, y, totalPlayers);
        // 计算新的最终位置
        var newPosition = cc.v2(basePosition.x + position.x, basePosition.y + position.y);
        // 播放平滑移动和缩小动画（多人格子情况）
        this.playAvatarAdjustAnimation(myPlayerNode, newPosition, position.scale);
    };
    /**
     * 在空格子上添加其他玩家头像
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param actions 其他玩家操作列表
     */
    ChessBoardController.prototype.addOtherPlayersToEmptyGrid = function (x, y, actions) {
        var totalPlayers = actions.length; // 空格子上只有其他玩家
        var positions = this.getPlayerPositions(totalPlayers);
        for (var i = 0; i < actions.length; i++) {
            var action = actions[i];
            var position = positions[i];
            // 使用棋盘坐标系创建其他玩家头像
            this.createOtherPlayerAtBoardPosition(x, y, action, position, totalPlayers);
        }
    };
    /**
     * 在棋盘坐标系中创建其他玩家头像（参考自己头像的生成逻辑）
     * @param gridX 格子x坐标
     * @param gridY 格子y坐标
     * @param action 玩家操作数据
     * @param relativePosition 相对于格子中心的位置和缩放
     * @param totalPlayers 该格子的总人数
     */
    ChessBoardController.prototype.createOtherPlayerAtBoardPosition = function (gridX, gridY, action, relativePosition, totalPlayers) {
        var _this = this;
        if (!this.playerGamePrefab) {
            console.error("playerGamePrefab 预制体未设置");
            return;
        }
        if (!this.boardNode) {
            console.error("棋盘节点未设置");
            return;
        }
        // 创建玩家预制体实例
        var playerNode = cc.instantiate(this.playerGamePrefab);
        // 根据总人数计算基础位置（统一逻辑）
        var basePosition = this.calculateBasePositionByPlayerCount(gridX, gridY, totalPlayers);
        // 添加相对偏移
        var finalPosition = cc.v2(basePosition.x + relativePosition.x, basePosition.y + relativePosition.y);
        playerNode.setPosition(finalPosition);
        playerNode.setScale(relativePosition.scale);
        // 先隐藏节点，等头像加载完成后再显示
        playerNode.active = false;
        // 安全地添加到棋盘节点（参考自己头像的添加逻辑）
        this.addPlayerNodeSafely(playerNode);
        // 设置其他玩家的头像和数据
        this.setupOtherPlayerData(playerNode, action, function () {
            // 头像加载完成的回调
            if (totalPlayers === 1) {
                // 单人格子：播放生成动画
                _this.playAvatarSpawnAnimation(playerNode);
            }
            else {
                // 多人格子：直接显示（其他人是新生成的，不需要动画）
                playerNode.active = true;
            }
        });
    };
    /**
     * 设置其他玩家的数据（参考自己头像的设置逻辑）
     * @param playerNode 玩家节点
     * @param action 玩家操作数据
     * @param onComplete 完成回调
     */
    ChessBoardController.prototype.setupOtherPlayerData = function (playerNode, action, onComplete) {
        try {
            var playerController_1 = playerNode.getComponent(PlayerGameController_1.default);
            if (!playerController_1) {
                console.error("❌ 找不到PlayerGameController组件");
                return;
            }
            // 从GlobalBean中获取真实的玩家数据
            var realUserData_1 = this.getRealUserData(action.userId);
            if (!realUserData_1) {
                console.warn("\u627E\u4E0D\u5230\u7528\u6237 " + action.userId + " \u7684\u771F\u5B9E\u6570\u636E");
                return;
            }
            // 在节点上存储userId信息，用于后续分数显示匹配
            playerNode['userId'] = action.userId;
            // 使用延迟设置，参考自己头像的设置逻辑
            this.scheduleOnce(function () {
                if (typeof playerController_1.setData === 'function') {
                    playerController_1.setData(realUserData_1);
                }
                // 根据操作类型设置旗子显示
                var withFlag = (action.action === 2); // action=2表示标记操作，显示旗子
                if (playerController_1.flagNode) {
                    playerController_1.flagNode.active = withFlag;
                }
                // 调用完成回调
                if (onComplete) {
                    onComplete();
                }
            }, 0.1);
        }
        catch (error) {
            console.warn("\u8BBE\u7F6E\u5176\u4ED6\u73A9\u5BB6\u6570\u636E\u65F6\u51FA\u9519: " + error);
        }
    };
    /**
     * 根据玩家数量获取布局位置
     * @param playerCount 玩家数量
     * @returns 位置数组 {x: number, y: number, scale: number}[]
     */
    ChessBoardController.prototype.getPlayerPositions = function (playerCount) {
        switch (playerCount) {
            case 1:
                // 单个玩家，居中显示，正常大小
                return [{ x: 0, y: 0, scale: 1.0 }];
            case 2:
                // 两个玩家，左右分布，缩放0.5
                return [
                    { x: -22, y: -8, scale: 0.5 },
                    { x: 22, y: -8, scale: 0.5 } // 右
                ];
            case 3:
                // 三个玩家，上中下分布，缩放0.5
                return [
                    { x: 0, y: 12, scale: 0.5 },
                    { x: -23, y: -27, scale: 0.5 },
                    { x: 23, y: -27, scale: 0.5 } // 右下
                ];
            case 4:
                // 四个玩家，四角分布，缩放0.5
                return [
                    { x: -22, y: 12, scale: 0.5 },
                    { x: 22, y: 12, scale: 0.5 },
                    { x: -22, y: -30, scale: 0.5 },
                    { x: 22, y: -30, scale: 0.5 } // 右下
                ];
            default:
                // 超过4个玩家，只显示前4个
                console.warn("\u73A9\u5BB6\u6570\u91CF\u8FC7\u591A: " + playerCount + "\uFF0C\u53EA\u663E\u793A\u524D4\u4E2A");
                return this.getPlayerPositions(4);
        }
    };
    /**
     * 获取指定位置的格子节点
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @returns 格子节点或null
     */
    ChessBoardController.prototype.getGridNode = function (x, y) {
        if (!this.boardNode || !this.isValidCoordinate(x, y)) {
            return null;
        }
        // 计算在棋盘子节点中的索引 (8x8棋盘，从左到右，从上到下)
        var index = y * this.BOARD_SIZE + x;
        if (index >= 0 && index < this.boardNode.children.length) {
            return this.boardNode.children[index];
        }
        return null;
    };
    /**
     * 在指定位置创建玩家预制体节点
     * @param gridNode 格子节点
     * @param action 玩家操作数据
     * @param position 相对位置和缩放
     */
    ChessBoardController.prototype.createPlayerNodeAtPosition = function (gridNode, action, position) {
        if (!this.playerGamePrefab) {
            console.error("playerGamePrefab 预制体未设置");
            return;
        }
        // 创建玩家预制体实例
        var playerNode = cc.instantiate(this.playerGamePrefab);
        // 检查预制体上的组件
        var components = playerNode.getComponents(cc.Component);
        components.forEach(function (comp, index) {
        });
        // 设置位置和缩放
        playerNode.setPosition(position.x, position.y);
        playerNode.setScale(position.scale);
        // 添加到格子节点
        gridNode.addChild(playerNode);
        // 设置玩家数据
        this.setupPlayerNodeData(playerNode, action);
    };
    /**
     * 设置玩家节点数据
     * @param playerNode 玩家节点
     * @param action 玩家操作数据
     */
    ChessBoardController.prototype.setupPlayerNodeData = function (playerNode, action) {
        try {
            var playerController = playerNode.getComponent(PlayerGameController_1.default);
            if (!playerController) {
                console.error("❌ 找不到PlayerGameController组件");
                var allComponents = playerNode.getComponents(cc.Component);
                allComponents.forEach(function (comp, index) {
                });
                return;
            }
            // 从GlobalBean中获取真实的玩家数据
            var realUserData = this.getRealUserData(action.userId);
            if (!realUserData) {
                console.warn("\u627E\u4E0D\u5230\u7528\u6237 " + action.userId + " \u7684\u771F\u5B9E\u6570\u636E");
                return;
            }
            if (typeof playerController.setData === 'function') {
                playerController.setData(realUserData);
            }
            // 根据操作类型设置旗子显示
            var withFlag = (action.action === 2); // action=2表示标记操作，显示旗子
            if (playerController.flagNode) {
                playerController.flagNode.active = withFlag;
            }
            else {
                console.warn("找不到flagNode节点");
            }
        }
        catch (error) {
            console.warn("\u8BBE\u7F6E\u73A9\u5BB6\u8282\u70B9\u6570\u636E\u65F6\u51FA\u9519: " + error);
        }
    };
    /**
     * 从GlobalBean中获取真实的用户数据
     * @param userId 用户ID
     * @returns RoomUser 或 null
     */
    ChessBoardController.prototype.getRealUserData = function (userId) {
        try {
            if (!GlobalBean_1.GlobalBean.GetInstance().noticeStartGame || !GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users) {
                console.warn("没有游戏数据，无法获取用户信息");
                return null;
            }
            var users = GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users;
            var user = users.find(function (u) { return u.userId === userId; });
            if (user) {
                return user;
            }
            else {
                console.warn("\u672A\u627E\u5230\u7528\u6237 " + userId + " \u7684\u6570\u636E");
                return null;
            }
        }
        catch (error) {
            console.error("\u83B7\u53D6\u7528\u6237\u6570\u636E\u65F6\u51FA\u9519: " + error);
            return null;
        }
    };
    /**
     * 在指定位置的玩家节点上显示分数
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param score 分数
     * @param showPlusOne 是否显示+1（先手奖励）
     */
    ChessBoardController.prototype.showScoreOnPlayerNode = function (x, y, score, showPlusOne) {
        var _this = this;
        if (!this.isValidCoordinate(x, y)) {
            console.warn("\u65E0\u6548\u5750\u6807: (" + x + ", " + y + ")");
            return;
        }
        // 查找该位置的玩家节点
        var playerNode = this.findPlayerNodeAtPosition(x, y);
        if (!playerNode) {
            console.warn("\u5728\u4F4D\u7F6E(" + x + ", " + y + ")\u627E\u4E0D\u5230\u73A9\u5BB6\u8282\u70B9");
            return;
        }
        // 获取PlayerGameController组件
        var playerController = playerNode.getComponent(PlayerGameController_1.default);
        if (!playerController) {
            console.warn("找不到PlayerGameController组件");
            return;
        }
        // 显示分数动画
        if (showPlusOne) {
            // 先显示+1，再显示本回合得分
            this.showScoreAnimationOnNode(playerController, 1, function () {
                _this.scheduleOnce(function () {
                    _this.showScoreAnimationOnNode(playerController, score, null);
                }, 1.0);
            });
        }
        else {
            // 只显示本回合得分
            this.showScoreAnimationOnNode(playerController, score, null);
        }
    };
    /**
     * 查找指定位置的玩家节点
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @returns 玩家节点或null
     */
    ChessBoardController.prototype.findPlayerNodeAtPosition = function (x, y) {
        // 方法1: 从gridData中查找（自己的头像）
        if (this.gridData[x][y].hasPlayer && this.gridData[x][y].playerNode) {
            return this.gridData[x][y].playerNode;
        }
        // 方法2: 在棋盘上查找其他玩家的头像
        if (!this.boardNode) {
            return null;
        }
        // 计算该位置的世界坐标
        var targetPosition = this.calculateCorrectPosition(x, y);
        // 遍历棋盘上的所有玩家节点，找到最接近目标位置的
        var closestNode = null;
        var minDistance = Number.MAX_VALUE;
        for (var i = 0; i < this.boardNode.children.length; i++) {
            var child = this.boardNode.children[i];
            var playerController = child.getComponent(PlayerGameController_1.default);
            if (playerController) {
                var distance = cc.Vec2.distance(child.getPosition(), targetPosition);
                if (distance < minDistance && distance < 50) { // 50像素的容差
                    minDistance = distance;
                    closestNode = child;
                }
            }
        }
        return closestNode;
    };
    /**
     * 在节点上显示分数动画
     * @param playerController 玩家控制器
     * @param score 分数
     * @param onComplete 完成回调
     */
    ChessBoardController.prototype.showScoreAnimationOnNode = function (playerController, score, onComplete) {
        // TODO: 实现在player_game_pfb上显示分数动画的逻辑
        // 这里需要根据PlayerGameController的具体实现来显示分数
        if (onComplete) {
            this.scheduleOnce(onComplete, 1.0);
        }
    };
    /**
     * 让指定位置的所有头像消失（参考回合结束时的清理逻辑）
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param onComplete 完成回调
     */
    ChessBoardController.prototype.hideAvatarsAtPosition = function (x, y, onComplete) {
        var _this = this;
        if (!this.boardNode) {
            console.warn("棋盘节点不存在，无法清理头像");
            onComplete();
            return;
        }
        // 收集所有头像节点（参考clearAllPlayerNodes的逻辑）
        var avatarNodes = [];
        // 方法1: 收集存储在gridData中的玩家节点（自己的头像）
        for (var gx = 0; gx < this.BOARD_SIZE; gx++) {
            for (var gy = 0; gy < this.BOARD_SIZE; gy++) {
                if (this.gridData[gx][gy].hasPlayer && this.gridData[gx][gy].playerNode) {
                    avatarNodes.push(this.gridData[gx][gy].playerNode);
                }
            }
        }
        // 方法2: 收集棋盘上所有的玩家预制体节点（包括其他玩家的头像）
        for (var i = 0; i < this.boardNode.children.length; i++) {
            var child = this.boardNode.children[i];
            // 检查是否是玩家预制体（通过组件判断，参考clearAllPlayerNodes）
            var playerController = child.getComponent(PlayerGameController_1.default);
            if (playerController) {
                // 避免重复添加（可能已经在方法1中添加过）
                if (!avatarNodes.includes(child)) {
                    avatarNodes.push(child);
                }
            }
        }
        if (avatarNodes.length === 0) {
            // 没有头像需要消失
            onComplete();
            return;
        }
        var completedCount = 0;
        var totalCount = avatarNodes.length;
        // 为每个头像播放消失动画
        avatarNodes.forEach(function (avatarNode, index) {
            // 使用cc.Tween播放消失动画
            cc.tween(avatarNode)
                .to(0.3, { opacity: 0, scaleX: 0.5, scaleY: 0.5 }, { easing: 'sineIn' })
                .call(function () {
                // 动画完成后移除节点
                avatarNode.removeFromParent();
                completedCount++;
                // 所有头像都消失完成后，执行回调
                if (completedCount >= totalCount) {
                    // 清除所有自己头像的引用（参考clearAllPlayerNodes）
                    _this.clearAllMyAvatarReferences();
                    onComplete();
                }
            })
                .start();
        });
    };
    /**
     * 清除所有自己头像的引用（参考clearAllPlayerNodes的逻辑）
     */
    ChessBoardController.prototype.clearAllMyAvatarReferences = function () {
        for (var x = 0; x < this.BOARD_SIZE; x++) {
            for (var y = 0; y < this.BOARD_SIZE; y++) {
                if (this.gridData[x][y].hasPlayer) {
                    this.gridData[x][y].hasPlayer = false;
                    this.gridData[x][y].playerNode = null;
                }
            }
        }
    };
    /**
     * 隐藏指定位置的格子（不销毁，以便重置时可以重新显示）
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param immediate 是否立即隐藏（不播放动画）
     */
    ChessBoardController.prototype.removeGridAt = function (x, y, immediate) {
        if (immediate === void 0) { immediate = false; }
        if (!this.isValidCoordinate(x, y)) {
            console.warn("\u9690\u85CF\u683C\u5B50\u5931\u8D25\uFF1A\u5750\u6807(" + x + ", " + y + ")\u65E0\u6548");
            return;
        }
        // 获取格子节点
        var gridNode = this.gridNodes[x] && this.gridNodes[x][y];
        if (gridNode) {
            if (immediate) {
                // 立即隐藏，不播放动画
                gridNode.active = false;
            }
            else {
                // 播放四边形格子消失动画
                this.playGridFallAnimation(gridNode);
            }
        }
    };
    /**
     * 播放四边形格子消失动画
     * 效果：格子持续旋转，给一个随机向上的力，然后旋转着自由落体
     * @param gridNode 格子节点
     */
    ChessBoardController.prototype.playGridFallAnimation = function (gridNode) {
        if (!gridNode)
            return;
        // 停止该格子上所有正在进行的动画（包括震动动画）
        gridNode.stopAllActions();
        // 保存格子的原始位置（用于重置时恢复）
        if (!gridNode['originalPosition']) {
            gridNode['originalPosition'] = gridNode.getPosition();
        }
        // 随机选择向上的力的方向：0=向上，1=右上15度，2=左上15度
        var forceDirection = Math.floor(Math.random() * 3);
        var moveX = 0;
        var moveY = 200; // 向上的基础距离（增加高度）
        switch (forceDirection) {
            case 0: // 向上
                moveX = 0;
                break;
            case 1: // 右上20度
                moveX = Math.sin(20 * Math.PI / 180) * moveY;
                break;
            case 2: // 左上20度
                moveX = -Math.sin(20 * Math.PI / 180) * moveY;
                break;
        }
        // 随机旋转速度
        var rotationSpeed = (Math.random() * 1440 + 720) * (Math.random() > 0.5 ? 1 : -1); // 720-2160度/秒，随机方向
        // 动画参数
        var upTime = 0.15; // 向上运动时间
        var fallTime = 0.3; // 下落时间
        var initialPosition = gridNode.getPosition();
        // 创建持续旋转的动画
        var rotationTween = cc.tween(gridNode)
            .repeatForever(cc.tween().by(0.1, { angle: rotationSpeed * 0.1 }));
        // 创建分阶段的运动动画
        var movementTween = cc.tween(gridNode)
            // 第一阶段：向上抛出
            .to(upTime, {
            x: initialPosition.x + moveX,
            y: initialPosition.y + moveY
        }, { easing: 'quadOut' })
            // 第二阶段：自由落体
            .to(fallTime, {
            x: initialPosition.x + moveX + (Math.random() - 0.5) * 100,
            y: initialPosition.y - 500 // 下落到屏幕下方更远处
        }, { easing: 'quadIn' })
            .call(function () {
            // 动画结束后隐藏格子
            gridNode.active = false;
            // 停止旋转动画
            gridNode.stopAllActions();
        });
        // 同时开始旋转和移动动画
        rotationTween.start();
        movementTween.start();
    };
    /**
     * 在指定位置创建boom预制体
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param isCurrentUser 是否是当前用户点到的雷（可选，默认为true以保持向后兼容）
     */
    ChessBoardController.prototype.createBoomPrefab = function (x, y, isCurrentUser) {
        var _this = this;
        if (isCurrentUser === void 0) { isCurrentUser = true; }
        if (!this.boomPrefab) {
            console.error("boomPrefab 预制体未设置，请在编辑器中挂载");
            return;
        }
        // 实例化您的boom预制体
        var boomNode = cc.instantiate(this.boomPrefab);
        boomNode.name = "Boom";
        // 设置位置（使用新的精确位置计算）
        var position = this.calculatePrefabPosition(x, y);
        boomNode.setPosition(position);
        // 添加到棋盘
        this.addPlayerNodeSafely(boomNode);
        // 播放出现动画
        boomNode.setScale(0);
        cc.tween(boomNode)
            .to(0.3, { scaleX: 1.2, scaleY: 1.2 }, { easing: 'backOut' })
            .to(0.1, { scaleX: 1.0, scaleY: 1.0 })
            .start();
        // 只有当前用户点到雷时才播放棋盘震动效果
        // 延迟0.45秒，等格子下落动画完成后再播放震动
        if (isCurrentUser) {
            this.scheduleOnce(function () {
                _this.playBoardShakeAnimation();
            }, 0.45);
        }
    };
    /**
     * 在指定位置创建biaoji预制体
     * @param x 格子x坐标
     * @param y 格子y坐标
     */
    ChessBoardController.prototype.createBiaojiPrefab = function (x, y) {
        if (!this.biaojiPrefab) {
            console.error("biaojiPrefab 预制体未设置，请在编辑器中挂载");
            return;
        }
        // 实例化您的biaoji预制体
        var biaojiNode = cc.instantiate(this.biaojiPrefab);
        biaojiNode.name = "Biaoji";
        // 设置位置（使用新的精确位置计算）
        var position = this.calculatePrefabPosition(x, y);
        biaojiNode.setPosition(position);
        // 添加到棋盘
        this.addPlayerNodeSafely(biaojiNode);
        // 播放出现动画
        biaojiNode.setScale(0);
        cc.tween(biaojiNode)
            .to(0.2, { scaleX: 1.0, scaleY: 1.0 }, { easing: 'backOut' })
            .start();
    };
    /**
     * 更新指定位置的neighborMines显示（使用boom数字预制体）
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param neighborMines 周围地雷数量
     */
    ChessBoardController.prototype.updateNeighborMinesDisplay = function (x, y, neighborMines) {
        // 0不需要显示数字
        if (neighborMines === 0) {
            return;
        }
        // 直接使用boom数字预制体
        this.createNumberPrefab(x, y, neighborMines);
    };
    /**
     * 创建数字预制体（boom1, boom2, ...）
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param number 数字
     */
    ChessBoardController.prototype.createNumberPrefab = function (x, y, number) {
        // 根据数字选择对应的预制体
        var prefab = null;
        switch (number) {
            case 1:
                prefab = this.boom1Prefab;
                break;
            case 2:
                prefab = this.boom2Prefab;
                break;
            case 3:
                prefab = this.boom3Prefab;
                break;
            case 4:
                prefab = this.boom4Prefab;
                break;
            case 5:
                prefab = this.boom5Prefab;
                break;
            case 6:
                prefab = this.boom6Prefab;
                break;
            case 7:
                prefab = this.boom7Prefab;
                break;
            case 8:
                prefab = this.boom8Prefab;
                break;
            default:
                console.error("\u4E0D\u652F\u6301\u7684\u6570\u5B57: " + number);
                return;
        }
        if (!prefab) {
            console.error("boom" + number + "Prefab \u9884\u5236\u4F53\u672A\u8BBE\u7F6E\uFF0C\u8BF7\u5728\u7F16\u8F91\u5668\u4E2D\u6302\u8F7D");
            return;
        }
        // 实例化数字预制体
        var numberNode = cc.instantiate(prefab);
        numberNode.name = "Boom" + number;
        // 设置位置（使用新的精确位置计算）
        var position = this.calculatePrefabPosition(x, y);
        numberNode.setPosition(position);
        // 添加到棋盘
        this.addPlayerNodeSafely(numberNode);
        // 播放出现动画
        numberNode.setScale(0);
        cc.tween(numberNode)
            .to(0.2, { scaleX: 1.0, scaleY: 1.0 }, { easing: 'backOut' })
            .start();
    };
    /**
     * 加载数字预制体
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param number 数字
     */
    ChessBoardController.prototype.loadNumberPrefab = function (x, y, number) {
        var prefabName = number + "boom";
        this.createTemporaryNumberNode(x, y, number);
    };
    /**
     * 创建临时的数字节点（在预制体加载失败时使用）
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param number 数字
     */
    ChessBoardController.prototype.createTemporaryNumberNode = function (x, y, number) {
        // 创建数字显示节点
        var numberNode = new cc.Node("NeighborMines_" + number);
        var label = numberNode.addComponent(cc.Label);
        // 设置数字显示 - 更大的字体和居中对齐
        label.string = number.toString();
        label.fontSize = 48; // 增大字体
        label.node.color = this.getNumberColor(number);
        label.horizontalAlign = cc.Label.HorizontalAlign.CENTER;
        label.verticalAlign = cc.Label.VerticalAlign.CENTER;
        // 设置节点大小，确保居中
        numberNode.setContentSize(this.GRID_SIZE, this.GRID_SIZE);
        // 设置位置 - 使用格子中心位置
        var position = this.calculateCorrectPosition(x, y);
        numberNode.setPosition(position);
        // 添加到棋盘
        this.addPlayerNodeSafely(numberNode);
        // 播放出现动画
        this.playNumberAppearAnimation(numberNode, number);
    };
    /**
     * 设置数字节点（用于预制体）
     * @param numberNode 数字节点
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param number 数字
     */
    ChessBoardController.prototype.setupNumberNode = function (numberNode, x, y, number) {
        // 设置位置 - 使用格子中心位置
        var position = this.calculateCorrectPosition(x, y);
        numberNode.setPosition(position);
        // 添加到棋盘
        this.addPlayerNodeSafely(numberNode);
        // 播放出现动画
        this.playNumberAppearAnimation(numberNode, number);
    };
    /**
     * 播放数字出现动画
     * @param numberNode 数字节点
     * @param number 数字
     */
    ChessBoardController.prototype.playNumberAppearAnimation = function (numberNode, number) {
        // 使用cc.Tween播放数字出现动画
        numberNode.setScale(0);
        cc.tween(numberNode)
            .to(0.2, { scaleX: 1.0, scaleY: 1.0 }, { easing: 'backOut' })
            .start();
    };
    /**
     * 播放格子消失动画（连锁效果）
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param neighborMines 周围地雷数量
     */
    ChessBoardController.prototype.playGridDisappearAnimation = function (x, y, neighborMines) {
        var _this = this;
        // 先删除格子
        this.removeGridAt(x, y);
        // 延迟0.3秒后显示数字（等格子消失动画完成）
        this.scheduleOnce(function () {
            _this.updateNeighborMinesDisplay(x, y, neighborMines);
        }, 0.3);
    };
    /**
     * 根据数字获取颜色
     * @param number 数字
     * @returns 颜色
     */
    ChessBoardController.prototype.getNumberColor = function (number) {
        switch (number) {
            case 1: return cc.Color.BLUE;
            case 2: return cc.Color.GREEN;
            case 3: return cc.Color.RED;
            case 4: return cc.Color.MAGENTA;
            case 5: return cc.Color.YELLOW;
            case 6: return cc.Color.CYAN;
            case 7: return cc.Color.BLACK;
            case 8: return cc.Color.GRAY;
            default: return cc.Color.BLACK;
        }
    };
    /**
     * 播放棋盘震动动画（包括所有小格子）
     */
    ChessBoardController.prototype.playBoardShakeAnimation = function () {
        if (!this.boardNode) {
            console.warn("boardNode 未设置，无法播放震动效果");
            return;
        }
        // 震动参数 - 增强震动效果
        var shakeIntensity = 30; // 震动强度
        var shakeDuration = 1.0; // 震动持续时间
        var shakeFrequency = 40; // 震动频率
        // 震动棋盘
        this.shakeBoardNode(shakeIntensity, shakeDuration, shakeFrequency);
        // 震动所有小格子
        this.shakeAllGrids(shakeIntensity * 0.6, shakeDuration, shakeFrequency);
    };
    /**
     * 震动棋盘节点
     */
    ChessBoardController.prototype.shakeBoardNode = function (intensity, duration, frequency) {
        // 保存原始位置
        var originalPosition = this.boardNode.position.clone();
        // 创建震动动画，使用递减强度
        var currentIntensity = intensity;
        var intensityDecay = 0.92; // 强度衰减系数
        var createShakeStep = function (shakeIntensity) {
            return cc.tween()
                .to(0.025, {
                x: originalPosition.x + (Math.random() - 0.5) * shakeIntensity * 2,
                y: originalPosition.y + (Math.random() - 0.5) * shakeIntensity * 2
            });
        };
        // 创建震动序列，强度逐渐衰减
        var shakeTween = cc.tween(this.boardNode);
        var totalSteps = Math.floor(duration * frequency);
        for (var i = 0; i < totalSteps; i++) {
            shakeTween = shakeTween.then(createShakeStep(currentIntensity));
            currentIntensity *= intensityDecay; // 逐渐减弱震动强度
        }
        // 最后恢复到原位置
        shakeTween.to(0.2, {
            x: originalPosition.x,
            y: originalPosition.y
        }, { easing: 'backOut' })
            .start();
    };
    /**
     * 震动所有小格子
     */
    ChessBoardController.prototype.shakeAllGrids = function (intensity, duration, frequency) {
        if (!this.gridNodes)
            return;
        // 遍历所有格子节点
        for (var x = 0; x < this.gridNodes.length; x++) {
            if (!this.gridNodes[x])
                continue;
            for (var y = 0; y < this.gridNodes[x].length; y++) {
                var gridNode = this.gridNodes[x][y];
                if (!gridNode || !gridNode.active)
                    continue;
                // 为每个格子创建独立的震动动画
                this.shakeGridNode(gridNode, intensity, duration, frequency);
            }
        }
    };
    /**
     * 震动单个格子节点
     */
    ChessBoardController.prototype.shakeGridNode = function (gridNode, intensity, duration, frequency) {
        // 保存原始位置
        var originalPosition = gridNode.position.clone();
        // 为每个格子添加随机延迟，创造波浪效果
        var randomDelay = Math.random() * 0.1;
        this.scheduleOnce(function () {
            // 创建震动动画，使用递减强度
            var currentIntensity = intensity;
            var intensityDecay = 0.94; // 格子震动衰减稍慢一些
            var createGridShakeStep = function (shakeIntensity) {
                return cc.tween()
                    .to(0.02, {
                    x: originalPosition.x + (Math.random() - 0.5) * shakeIntensity * 2,
                    y: originalPosition.y + (Math.random() - 0.5) * shakeIntensity * 2
                });
            };
            // 创建震动序列
            var shakeTween = cc.tween(gridNode);
            var totalSteps = Math.floor(duration * frequency * 0.8); // 格子震动时间稍短
            for (var i = 0; i < totalSteps; i++) {
                shakeTween = shakeTween.then(createGridShakeStep(currentIntensity));
                currentIntensity *= intensityDecay;
            }
            // 最后恢复到原位置
            shakeTween.to(0.15, {
                x: originalPosition.x,
                y: originalPosition.y
            }, { easing: 'backOut' })
                .start();
        }, randomDelay);
    };
    __decorate([
        property(cc.Prefab)
    ], ChessBoardController.prototype, "playerGamePrefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], ChessBoardController.prototype, "boomPrefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], ChessBoardController.prototype, "biaojiPrefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], ChessBoardController.prototype, "boom1Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], ChessBoardController.prototype, "boom2Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], ChessBoardController.prototype, "boom3Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], ChessBoardController.prototype, "boom4Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], ChessBoardController.prototype, "boom5Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], ChessBoardController.prototype, "boom6Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], ChessBoardController.prototype, "boom7Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], ChessBoardController.prototype, "boom8Prefab", void 0);
    __decorate([
        property(cc.Node)
    ], ChessBoardController.prototype, "boardNode", void 0);
    ChessBoardController = __decorate([
        ccclass
    ], ChessBoardController);
    return ChessBoardController;
}(cc.Component));
exports.default = ChessBoardController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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