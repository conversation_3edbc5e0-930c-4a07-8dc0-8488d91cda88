
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/game/Chess/SingleChessBoardController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '04af7LUaqhJFrIS/DTERlBy', 'SingleChessBoardController');
// scripts/game/Chess/SingleChessBoardController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var WebSocketManager_1 = require("../../net/WebSocketManager");
var MessageId_1 = require("../../net/MessageId");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
// 五种棋盘配置
var BOARD_CONFIGS = {
    "8x8": {
        width: 752,
        height: 752,
        rows: 8,
        cols: 8,
        gridWidth: 88,
        gridHeight: 88
    },
    "8x9": {
        width: 752,
        height: 845,
        rows: 9,
        cols: 8,
        gridWidth: 88,
        gridHeight: 88
    },
    "9x9": {
        width: 752,
        height: 747,
        rows: 9,
        cols: 9,
        gridWidth: 76,
        gridHeight: 76
    },
    "9x10": {
        width: 752,
        height: 830,
        rows: 10,
        cols: 9,
        gridWidth: 78,
        gridHeight: 78
    },
    "10x10": {
        width: 752,
        height: 745,
        rows: 10,
        cols: 10,
        gridWidth: 69,
        gridHeight: 69
    }
};
var SingleChessBoardController = /** @class */ (function (_super) {
    __extends(SingleChessBoardController, _super);
    function SingleChessBoardController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.boomPrefab = null; // boom预制体
        _this.biaojiPrefab = null; // biaoji预制体
        _this.boom1Prefab = null; // 数字1预制体
        _this.boom2Prefab = null; // 数字2预制体
        _this.boom3Prefab = null; // 数字3预制体
        _this.boom4Prefab = null; // 数字4预制体
        _this.boom5Prefab = null; // 数字5预制体
        _this.boom6Prefab = null; // 数字6预制体
        _this.boom7Prefab = null; // 数字7预制体
        _this.boom8Prefab = null; // 数字8预制体
        // 五个棋盘节点
        _this.qipan8x8Node = null; // 8x8棋盘节点
        _this.qipan8x9Node = null; // 8x9棋盘节点
        _this.qipan9x9Node = null; // 9x9棋盘节点
        _this.qipan9x10Node = null; // 9x10棋盘节点
        _this.qipan10x10Node = null; // 10x10棋盘节点
        // 当前使用的棋盘节点
        _this.currentBoardNode = null;
        // 当前棋盘配置
        _this.currentBoardConfig = null;
        _this.currentBoardType = "8x8"; // 默认8x8棋盘
        // 炸弹爆炸标记
        _this.hasBombExploded = false;
        // 格子数据存储
        _this.gridData = []; // 二维数组存储格子数据
        _this.gridNodes = []; // 二维数组存储格子节点
        // 防重复发送消息
        _this.lastClickTime = 0;
        _this.lastClickPosition = "";
        _this.CLICK_COOLDOWN = 200; // 200毫秒冷却时间
        return _this;
    }
    SingleChessBoardController.prototype.onLoad = function () {
        // 不进行默认初始化，等待外部调用initBoard
    };
    SingleChessBoardController.prototype.start = function () {
        // start方法不再自动启用触摸事件，避免与initBoard重复
        // 触摸事件的启用由initBoard方法负责
    };
    /**
     * 根据棋盘类型获取对应的棋盘节点
     * @param boardType 棋盘类型
     */
    SingleChessBoardController.prototype.getBoardNodeByType = function (boardType) {
        switch (boardType) {
            case "8x8":
                return this.qipan8x8Node;
            case "8x9":
                return this.qipan8x9Node;
            case "9x9":
                return this.qipan9x9Node;
            case "9x10":
                return this.qipan9x10Node;
            case "10x10":
                return this.qipan10x10Node;
            default:
                return null;
        }
    };
    /**
     * 初始化指定类型的棋盘
     * @param boardType 棋盘类型 ("8x8", "8x9", "9x9", "9x10", "10x10")
     */
    SingleChessBoardController.prototype.initBoard = function (boardType) {
        if (!BOARD_CONFIGS[boardType]) {
            console.error("\u4E0D\u652F\u6301\u7684\u68CB\u76D8\u7C7B\u578B: " + boardType);
            return;
        }
        // 根据棋盘类型获取对应的节点
        this.currentBoardNode = this.getBoardNodeByType(boardType);
        if (!this.currentBoardNode) {
            console.error("\u68CB\u76D8\u8282\u70B9\u672A\u8BBE\u7F6E\uFF01\u68CB\u76D8\u7C7B\u578B: " + boardType);
            return;
        }
        this.currentBoardType = boardType;
        this.currentBoardConfig = BOARD_CONFIGS[boardType];
        // 清空现有数据
        this.gridData = [];
        this.gridNodes = [];
        // 初始化数据数组
        for (var x = 0; x < this.currentBoardConfig.cols; x++) {
            this.gridData[x] = [];
            this.gridNodes[x] = [];
            for (var y = 0; y < this.currentBoardConfig.rows; y++) {
                this.gridData[x][y] = {
                    x: x,
                    y: y,
                    worldPos: this.getGridWorldPosition(x, y),
                    hasPlayer: false
                };
            }
        }
        this.createGridNodes();
    };
    // 启用现有格子的触摸事件
    SingleChessBoardController.prototype.createGridNodes = function () {
        if (!this.currentBoardNode) {
            console.error("棋盘节点未设置！");
            return;
        }
        // 如果格子已经存在，直接启用触摸事件
        this.enableTouchForExistingGrids();
    };
    // 为现有格子启用触摸事件
    SingleChessBoardController.prototype.enableTouchForExistingGrids = function () {
        // 检查棋盘节点是否存在
        if (!this.currentBoardNode) {
            console.error("棋盘节点未设置，无法启用触摸事件！");
            return;
        }
        // 遍历棋盘节点的所有子节点
        var children = this.currentBoardNode.children;
        for (var i = 0; i < children.length; i++) {
            var child = children[i];
            // 尝试从节点名称解析坐标
            var coords = this.parseGridCoordinateFromName(child.name);
            if (coords) {
                this.setupGridTouchEvents(child, coords.x, coords.y);
                this.gridNodes[coords.x] = this.gridNodes[coords.x] || [];
                this.gridNodes[coords.x][coords.y] = child;
            }
            else {
                // 如果无法从名称解析，尝试从位置计算
                var pos = child.getPosition();
                var coords_1 = this.getGridCoordinateFromPosition(pos);
                if (coords_1) {
                    this.setupGridTouchEvents(child, coords_1.x, coords_1.y);
                    this.gridNodes[coords_1.x] = this.gridNodes[coords_1.x] || [];
                    this.gridNodes[coords_1.x][coords_1.y] = child;
                }
            }
        }
    };
    // 从节点名称解析格子坐标
    SingleChessBoardController.prototype.parseGridCoordinateFromName = function (nodeName) {
        // 尝试匹配 Grid_x_y 格式
        var match = nodeName.match(/Grid_(\d+)_(\d+)/);
        if (match) {
            return { x: parseInt(match[1]), y: parseInt(match[2]) };
        }
        return null;
    };
    // 从位置计算格子坐标（需要考虑不同棋盘类型的边距）
    SingleChessBoardController.prototype.getGridCoordinateFromPosition = function (pos) {
        if (!this.currentBoardConfig)
            return null;
        // 根据不同棋盘类型使用不同的计算方式
        switch (this.currentBoardType) {
            case "8x9":
                return this.getGridCoordinateFromPositionFor8x9(pos);
            case "9x9":
                return this.getGridCoordinateFromPositionFor9x9(pos);
            case "9x10":
                return this.getGridCoordinateFromPositionFor9x10(pos);
            case "10x10":
                return this.getGridCoordinateFromPositionFor10x10(pos);
            default:
                // 默认计算方式（适用于其他棋盘类型）
                var x = Math.floor((pos.x + this.currentBoardConfig.width / 2) / this.currentBoardConfig.gridWidth);
                var y = Math.floor((pos.y + this.currentBoardConfig.height / 2) / this.currentBoardConfig.gridHeight);
                if (this.isValidCoordinate(x, y)) {
                    return { x: x, y: y };
                }
                return null;
        }
    };
    // 8x9棋盘的位置到坐标转换（基于精确坐标，左下角为(0,0)）
    SingleChessBoardController.prototype.getGridCoordinateFromPositionFor8x9 = function (pos) {
        // 使用与预制体位置计算相同的精确参数
        var startX = -321; // 左下角X坐标
        var startY = -364; // 左下角Y坐标
        var stepX = 91.14; // X方向精确步长
        var stepY = 91.125; // Y方向精确步长
        // 从位置反推坐标
        var x = Math.round((pos.x - startX) / stepX);
        var y = Math.round((pos.y - startY) / stepY);
        if (this.isValidCoordinate(x, y)) {
            return { x: x, y: y };
        }
        return null;
    };
    // 9x9棋盘的位置到坐标转换（基于精确坐标，左下角为(0,0)）
    SingleChessBoardController.prototype.getGridCoordinateFromPositionFor9x9 = function (pos) {
        // 使用与预制体位置计算相同的精确参数
        var startX = -322; // 左下角X坐标
        var startY = -320; // 左下角Y坐标
        var stepX = 80.25; // X方向精确步长
        var stepY = 80.375; // Y方向精确步长
        // 从位置反推坐标
        var x = Math.round((pos.x - startX) / stepX);
        var y = Math.round((pos.y - startY) / stepY);
        if (this.isValidCoordinate(x, y)) {
            return { x: x, y: y };
        }
        return null;
    };
    // 9x10棋盘的位置到坐标转换（基于精确坐标，左下角为(0,0)）
    SingleChessBoardController.prototype.getGridCoordinateFromPositionFor9x10 = function (pos) {
        // 使用与预制体位置计算相同的精确参数
        var startX = -320; // 左下角X坐标
        var startY = -361; // 左下角Y坐标
        var stepX = 80; // X方向精确步长
        var stepY = 80.33; // Y方向精确步长
        // 从位置反推坐标
        var x = Math.round((pos.x - startX) / stepX);
        var y = Math.round((pos.y - startY) / stepY);
        if (this.isValidCoordinate(x, y)) {
            return { x: x, y: y };
        }
        return null;
    };
    // 10x10棋盘的位置到坐标转换（基于精确坐标，左下角为(0,0)）
    SingleChessBoardController.prototype.getGridCoordinateFromPositionFor10x10 = function (pos) {
        // 使用与预制体位置计算相同的精确参数
        var startX = -328; // 左下角X坐标
        var startY = -322; // 左下角Y坐标
        var stepX = 72.56; // X方向精确步长
        var stepY = 72; // Y方向精确步长
        // 从位置反推坐标
        var x = Math.round((pos.x - startX) / stepX);
        var y = Math.round((pos.y - startY) / stepY);
        if (this.isValidCoordinate(x, y)) {
            return { x: x, y: y };
        }
        else {
            return null;
        }
    };
    // 为格子设置触摸事件
    SingleChessBoardController.prototype.setupGridTouchEvents = function (gridNode, x, y) {
        var _this = this;
        // 先清除已有的触摸事件，防止重复绑定
        gridNode.off(cc.Node.EventType.TOUCH_START);
        gridNode.off(cc.Node.EventType.TOUCH_END);
        gridNode.off(cc.Node.EventType.TOUCH_CANCEL);
        // 长按相关变量
        var isLongPressing = false;
        var longPressTimer = 0;
        var longPressCallback = null;
        var hasTriggeredLongPress = false; // 标记是否已触发长按
        var LONG_PRESS_TIME = 1.0; // 1秒长按时间
        // 触摸开始事件
        gridNode.on(cc.Node.EventType.TOUCH_START, function (_event) {
            isLongPressing = true;
            longPressTimer = 0;
            hasTriggeredLongPress = false; // 重置长按标记
            // 开始长按检测
            longPressCallback = function () {
                if (isLongPressing) {
                    longPressTimer += 0.1;
                    if (longPressTimer >= LONG_PRESS_TIME && !hasTriggeredLongPress) {
                        hasTriggeredLongPress = true; // 标记已触发长按
                        isLongPressing = false; // 立即停止长按状态，防止触摸结束时执行点击
                        // 执行长按事件
                        _this.onGridLongPress(x, y);
                        // 停止长按检测
                        if (longPressCallback) {
                            _this.unschedule(longPressCallback);
                            longPressCallback = null;
                        }
                    }
                }
            };
            _this.schedule(longPressCallback, 0.1);
        }, this);
        // 触摸结束事件
        gridNode.on(cc.Node.EventType.TOUCH_END, function (event) {
            // 停止长按检测
            if (longPressCallback) {
                _this.unschedule(longPressCallback);
                longPressCallback = null;
            }
            // 严格检查：只有在所有条件都满足的情况下才执行点击事件
            var shouldExecuteClick = isLongPressing &&
                longPressTimer < LONG_PRESS_TIME &&
                !hasTriggeredLongPress;
            if (shouldExecuteClick) {
                _this.onGridClick(x, y, event);
            }
            else {
            }
            // 清理长按检测
            isLongPressing = false;
            hasTriggeredLongPress = false;
        }, this);
        // 触摸取消事件
        gridNode.on(cc.Node.EventType.TOUCH_CANCEL, function (_event) {
            // 清理长按检测
            isLongPressing = false;
            hasTriggeredLongPress = false;
            if (longPressCallback) {
                _this.unschedule(longPressCallback);
                longPressCallback = null;
            }
        }, this);
    };
    // 计算格子的世界坐标位置（左下角为(0,0)）
    SingleChessBoardController.prototype.getGridWorldPosition = function (x, y) {
        if (!this.currentBoardConfig)
            return cc.v2(0, 0);
        // 计算格子中心点位置
        // 左下角为(0,0)，所以y坐标需要从下往上计算
        var posX = (x * this.currentBoardConfig.gridWidth) + (this.currentBoardConfig.gridWidth / 2) - (this.currentBoardConfig.width / 2);
        var posY = (y * this.currentBoardConfig.gridHeight) + (this.currentBoardConfig.gridHeight / 2) - (this.currentBoardConfig.height / 2);
        return cc.v2(posX, posY);
    };
    // 格子点击事件 - 发送挖掘操作
    SingleChessBoardController.prototype.onGridClick = function (x, y, _event) {
        // 检查坐标是否有效
        if (!this.isValidCoordinate(x, y)) {
            return;
        }
        // 检查该位置是否已经有任何预制体（包括biaoji）
        if (this.gridData[x][y].hasPlayer) {
            return;
        }
        // 发送LevelClickBlock消息
        this.sendLevelClickBlock(x, y, 1);
    };
    // 格子长按事件 - 标记/取消标记操作（参考联机版逻辑）
    SingleChessBoardController.prototype.onGridLongPress = function (x, y) {
        // 检查坐标是否有效
        if (!this.isValidCoordinate(x, y)) {
            return;
        }
        // 检查该位置是否已经有biaoji预制体
        if (this.hasBiaojiAt(x, y)) {
            // 如果已经有biaoji，则删除它（本地立即处理）
            this.removeBiaojiAt(x, y);
            // 发送取消标记消息（但不等待响应，因为已经本地处理了）
            this.sendLevelClickBlock(x, y, 2);
        }
        else if (!this.gridData[x][y].hasPlayer) {
            // 如果没有任何预制体，则生成biaoji（本地立即处理）
            this.createBiaojiPrefab(x, y);
            // 发送标记消息（但不等待响应，因为已经本地处理了）
            this.sendLevelClickBlock(x, y, 2);
        }
        else {
            // 如果有其他类型的预制体（如数字、boom），则不处理
        }
    };
    // 发送LevelClickBlock消息
    SingleChessBoardController.prototype.sendLevelClickBlock = function (x, y, action) {
        // 防重复发送检查
        var currentTime = Date.now();
        var positionKey = x + "," + y + "," + action;
        if (currentTime - this.lastClickTime < this.CLICK_COOLDOWN && this.lastClickPosition === positionKey) {
            return;
        }
        this.lastClickTime = currentTime;
        this.lastClickPosition = positionKey;
        var clickData = {
            x: x,
            y: y,
            action: action // 1=挖掘方块，2=标记/取消标记地雷
        };
        WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeLevelClickBlock, clickData);
    };
    // 检查坐标是否有效
    SingleChessBoardController.prototype.isValidCoordinate = function (x, y) {
        if (!this.currentBoardConfig)
            return false;
        return x >= 0 && x < this.currentBoardConfig.cols && y >= 0 && y < this.currentBoardConfig.rows;
    };
    /**
     * 在指定位置创建biaoji预制体
     * @param x 格子x坐标
     * @param y 格子y坐标
     */
    SingleChessBoardController.prototype.createBiaojiPrefab = function (x, y) {
        if (!this.biaojiPrefab) {
            console.error("biaojiPrefab 预制体未设置，请在编辑器中挂载");
            return;
        }
        // 实例化biaoji预制体
        var biaojiNode = cc.instantiate(this.biaojiPrefab);
        biaojiNode.name = "Biaoji";
        // 设置位置
        var position = this.calculatePrefabPosition(x, y);
        biaojiNode.setPosition(position);
        // 添加到棋盘
        this.currentBoardNode.addChild(biaojiNode);
        // 播放出现动画，10x10棋盘使用0.8缩放
        var targetScale = this.currentBoardType === "10x10" ? 0.8 : 1.0;
        biaojiNode.setScale(0);
        cc.tween(biaojiNode)
            .to(0.2, { scaleX: targetScale, scaleY: targetScale }, { easing: 'backOut' })
            .start();
        // 更新格子数据
        this.gridData[x][y].hasPlayer = true;
        this.gridData[x][y].playerNode = biaojiNode;
    };
    /**
     * 在指定位置创建boom预制体
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param isCurrentUser 是否是当前用户点到的雷（可选，默认为true以保持向后兼容）
     */
    SingleChessBoardController.prototype.createBoomPrefab = function (x, y, isCurrentUser) {
        if (isCurrentUser === void 0) { isCurrentUser = true; }
        if (!this.boomPrefab) {
            console.error("SingleChessBoardController: boomPrefab 预制体未设置，请在编辑器中挂载");
            return;
        }
        // 实例化boom预制体
        var boomNode = cc.instantiate(this.boomPrefab);
        boomNode.name = "Boom";
        // 设置位置
        var position = this.calculatePrefabPosition(x, y);
        boomNode.setPosition(position);
        // 添加到棋盘
        this.currentBoardNode.addChild(boomNode);
        // 播放出现动画，10x10棋盘使用0.8缩放
        var targetScale = this.currentBoardType === "10x10" ? 0.8 : 1.0;
        var bounceScale = targetScale * 1.2; // 弹跳效果比目标缩放大20%
        boomNode.setScale(0);
        cc.tween(boomNode)
            .to(0.3, { scaleX: bounceScale, scaleY: bounceScale }, { easing: 'backOut' })
            .to(0.1, { scaleX: targetScale, scaleY: targetScale })
            .start();
        // 只有当前用户点到雷时才播放棋盘震动效果
        if (isCurrentUser) {
            this.playBoardShakeAnimation();
        }
        // 更新格子数据
        this.gridData[x][y].hasPlayer = true;
        this.gridData[x][y].playerNode = boomNode;
    };
    /**
     * 在指定位置创建数字预制体
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param number 数字(1-8)
     */
    SingleChessBoardController.prototype.createNumberPrefab = function (x, y, number) {
        if (number < 1 || number > 8) {
            console.error("\u65E0\u6548\u7684\u6570\u5B57: " + number);
            return;
        }
        // 获取对应的数字预制体
        var prefab = this.getNumberPrefab(number);
        if (!prefab) {
            console.error("\u6570\u5B57" + number + "\u9884\u5236\u4F53\u672A\u8BBE\u7F6E");
            return;
        }
        // 实例化数字预制体
        var numberNode = cc.instantiate(prefab);
        numberNode.name = "Boom" + number;
        // 设置位置
        var position = this.calculatePrefabPosition(x, y);
        numberNode.setPosition(position);
        // 添加到棋盘
        this.currentBoardNode.addChild(numberNode);
        // 播放出现动画，10x10棋盘使用0.8缩放
        var targetScale = this.currentBoardType === "10x10" ? 0.8 : 1.0;
        numberNode.setScale(0);
        cc.tween(numberNode)
            .to(0.2, { scaleX: targetScale, scaleY: targetScale }, { easing: 'backOut' })
            .start();
    };
    /**
     * 在指定位置创建自定义预制体（用于测试等功能）
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param prefab 要创建的预制体
     * @param name 节点名称
     */
    SingleChessBoardController.prototype.createCustomPrefab = function (x, y, prefab, name) {
        if (name === void 0) { name = "CustomPrefab"; }
        if (!prefab) {
            console.error("预制体未设置");
            return null;
        }
        if (!this.currentBoardNode) {
            console.error("currentBoardNode 未设置，无法添加预制体");
            return null;
        }
        try {
            // 实例化预制体
            var customNode = cc.instantiate(prefab);
            customNode.name = name;
            // 设置位置
            var position = this.calculatePrefabPosition(x, y);
            customNode.setPosition(position);
            // 添加到棋盘
            this.currentBoardNode.addChild(customNode);
            // 播放出现动画，10x10棋盘使用0.8缩放
            var targetScale = this.currentBoardType === "10x10" ? 0.8 : 1.0;
            customNode.setScale(0);
            cc.tween(customNode)
                .to(0.3, { scaleX: targetScale * 1.2, scaleY: targetScale * 1.2 }, { easing: 'backOut' })
                .to(0.1, { scaleX: targetScale, scaleY: targetScale })
                .start();
            return customNode;
        }
        catch (error) {
            cc.error("❌ 创建自定义预制体时发生错误:", error);
            return null;
        }
    };
    // 获取数字预制体
    SingleChessBoardController.prototype.getNumberPrefab = function (number) {
        switch (number) {
            case 1: return this.boom1Prefab;
            case 2: return this.boom2Prefab;
            case 3: return this.boom3Prefab;
            case 4: return this.boom4Prefab;
            case 5: return this.boom5Prefab;
            case 6: return this.boom6Prefab;
            case 7: return this.boom7Prefab;
            case 8: return this.boom8Prefab;
            default: return null;
        }
    };
    /**
     * 计算预制体的精确位置（参考联机版ChessBoardController）
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @returns 预制体应该放置的精确位置
     */
    SingleChessBoardController.prototype.calculatePrefabPosition = function (x, y) {
        if (!this.currentBoardConfig) {
            return cc.v2(0, 0);
        }
        // 根据不同棋盘类型使用不同的计算方式
        switch (this.currentBoardType) {
            case "8x8":
                return this.calculatePrefabPositionFor8x8(x, y);
            case "8x9":
                return this.calculatePrefabPositionFor8x9(x, y);
            case "9x9":
                return this.calculatePrefabPositionFor9x9(x, y);
            case "9x10":
                return this.calculatePrefabPositionFor9x10(x, y);
            case "10x10":
                return this.calculatePrefabPositionFor10x10(x, y);
            default:
                return this.getGridWorldPosition(x, y);
        }
    };
    // 8x8棋盘的预制体位置计算（参考联机版）
    SingleChessBoardController.prototype.calculatePrefabPositionFor8x8 = function (x, y) {
        // 根据联机版的坐标规律计算：
        // (0,0) → (-314, -310)
        // (1,0) → (-224, -310)  // x增加90
        // (0,1) → (-314, -222)  // y增加88
        // (7,7) → (310, 312)
        var startX = -314; // 起始X坐标
        var startY = -310; // 起始Y坐标
        var stepX = 90; // X方向步长
        var stepY = 88; // Y方向步长
        var finalX = startX + (x * stepX);
        var finalY = startY + (y * stepY);
        return cc.v2(finalX, finalY);
    };
    // 8x9棋盘的预制体位置计算（基于精确坐标，左下角为(0,0)）
    SingleChessBoardController.prototype.calculatePrefabPositionFor8x9 = function (x, y) {
        // 根据提供的精确坐标：
        // 左下角(0,0)：(-321, -364)
        // 右下角(7,0)：(317, -364)
        // 左上角(0,8)：(-321, 365)
        // 右上角(7,8)：(317, 365)
        // 计算步长：
        // X方向步长：(317 - (-321)) / 7 = 638 / 7 ≈ 91.14
        // Y方向步长：(365 - (-364)) / 8 = 729 / 8 ≈ 91.125
        var startX = -321; // 左下角X坐标
        var startY = -364; // 左下角Y坐标
        var stepX = 91.14; // X方向精确步长
        var stepY = 91.125; // Y方向精确步长
        var finalX = startX + (x * stepX);
        var finalY = startY + (y * stepY);
        return cc.v2(finalX, finalY);
    };
    // 9x9棋盘的预制体位置计算（基于精确坐标，左下角为(0,0)）
    SingleChessBoardController.prototype.calculatePrefabPositionFor9x9 = function (x, y) {
        // 根据提供的精确坐标：
        // 左下角(0,0)：(-322, -320)
        // 右下角(8,0)：(320, -320)
        // 左上角(0,8)：(-322, 365)
        // 右上角(8,8)：(320, 323)
        // 计算步长：
        // X方向步长：(320 - (-322)) / 8 = 642 / 8 = 80.25
        // Y方向步长：(323 - (-320)) / 8 = 643 / 8 = 80.375
        var startX = -322; // 左下角X坐标
        var startY = -320; // 左下角Y坐标
        var stepX = 80.25; // X方向精确步长
        var stepY = 80.375; // Y方向精确步长
        var finalX = startX + (x * stepX);
        var finalY = startY + (y * stepY);
        return cc.v2(finalX, finalY);
    };
    // 9x10棋盘的预制体位置计算（基于精确坐标，左下角为(0,0)）
    SingleChessBoardController.prototype.calculatePrefabPositionFor9x10 = function (x, y) {
        // 根据提供的精确坐标：
        // 左下角(0,0)：(-320, -361)
        // 右下角(8,0)：(320, -361)
        // 左上角(0,9)：(-320, 362)
        // 右上角(8,9)：(320, 362)
        // 计算步长：
        // X方向步长：(320 - (-320)) / 8 = 640 / 8 = 80
        // Y方向步长：(362 - (-361)) / 9 = 723 / 9 = 80.33
        var startX = -320; // 左下角X坐标
        var startY = -361; // 左下角Y坐标
        var stepX = 80; // X方向精确步长
        var stepY = 80.33; // Y方向精确步长
        var finalX = startX + (x * stepX);
        var finalY = startY + (y * stepY);
        return cc.v2(finalX, finalY);
    };
    // 10x10棋盘的预制体位置计算（基于精确坐标，左下角为(0,0)）
    SingleChessBoardController.prototype.calculatePrefabPositionFor10x10 = function (x, y) {
        // 根据提供的精确坐标：
        // 左下角(0,0)：(-328, -322)
        // 右下角(9,0)：(325, -322)
        // 左上角(0,9)：(-328, 326)
        // 右上角(9,9)：(325, 326)
        // 计算步长：
        // X方向步长：(325 - (-328)) / 9 = 653 / 9 = 72.56
        // Y方向步长：(326 - (-322)) / 9 = 648 / 9 = 72
        var startX = -328; // 左下角X坐标
        var startY = -322; // 左下角Y坐标
        var stepX = 72.56; // X方向精确步长
        var stepY = 72; // Y方向精确步长
        var finalX = startX + (x * stepX);
        var finalY = startY + (y * stepY);
        return cc.v2(finalX, finalY);
    };
    /**
     * 播放棋盘震动动画（包括所有小格子）
     */
    SingleChessBoardController.prototype.playBoardShakeAnimation = function () {
        if (!this.currentBoardNode)
            return;
        // 震动参数 - 增强震动效果
        var shakeIntensity = 30; // 震动强度
        var shakeDuration = 1.0; // 震动持续时间
        var shakeFrequency = 40; // 震动频率
        // 震动棋盘
        this.shakeBoardNode(shakeIntensity, shakeDuration, shakeFrequency);
        // 震动所有小格子
        this.shakeAllGrids(shakeIntensity * 0.6, shakeDuration, shakeFrequency);
    };
    /**
     * 震动棋盘节点
     */
    SingleChessBoardController.prototype.shakeBoardNode = function (intensity, duration, frequency) {
        // 保存原始位置
        var originalPosition = this.currentBoardNode.position.clone();
        // 创建震动动画，使用递减强度
        var currentIntensity = intensity;
        var intensityDecay = 0.92; // 强度衰减系数
        var createShakeStep = function (shakeIntensity) {
            return cc.tween()
                .to(0.025, {
                x: originalPosition.x + (Math.random() - 0.5) * shakeIntensity * 2,
                y: originalPosition.y + (Math.random() - 0.5) * shakeIntensity * 2
            });
        };
        // 创建震动序列，强度逐渐衰减
        var shakeTween = cc.tween(this.currentBoardNode);
        var totalSteps = Math.floor(duration * frequency);
        for (var i = 0; i < totalSteps; i++) {
            shakeTween = shakeTween.then(createShakeStep(currentIntensity));
            currentIntensity *= intensityDecay; // 逐渐减弱震动强度
        }
        // 最后恢复到原位置
        shakeTween.to(0.2, {
            x: originalPosition.x,
            y: originalPosition.y
        }, { easing: 'backOut' })
            .start();
    };
    /**
     * 震动所有小格子
     */
    SingleChessBoardController.prototype.shakeAllGrids = function (intensity, duration, frequency) {
        if (!this.gridNodes)
            return;
        // 遍历所有格子节点
        for (var x = 0; x < this.gridNodes.length; x++) {
            if (!this.gridNodes[x])
                continue;
            for (var y = 0; y < this.gridNodes[x].length; y++) {
                var gridNode = this.gridNodes[x][y];
                if (!gridNode || !gridNode.active)
                    continue;
                // 为每个格子创建独立的震动动画
                this.shakeGridNode(gridNode, intensity, duration, frequency);
            }
        }
    };
    /**
     * 震动单个格子节点
     */
    SingleChessBoardController.prototype.shakeGridNode = function (gridNode, intensity, duration, frequency) {
        // 保存原始位置
        var originalPosition = gridNode.position.clone();
        // 为每个格子添加随机延迟，创造波浪效果
        var randomDelay = Math.random() * 0.1;
        this.scheduleOnce(function () {
            // 创建震动动画，使用递减强度
            var currentIntensity = intensity;
            var intensityDecay = 0.94; // 格子震动衰减稍慢一些
            var createGridShakeStep = function (shakeIntensity) {
                return cc.tween()
                    .to(0.02, {
                    x: originalPosition.x + (Math.random() - 0.5) * shakeIntensity * 2,
                    y: originalPosition.y + (Math.random() - 0.5) * shakeIntensity * 2
                });
            };
            // 创建震动序列
            var shakeTween = cc.tween(gridNode);
            var totalSteps = Math.floor(duration * frequency * 0.8); // 格子震动时间稍短
            for (var i = 0; i < totalSteps; i++) {
                shakeTween = shakeTween.then(createGridShakeStep(currentIntensity));
                currentIntensity *= intensityDecay;
            }
            // 最后恢复到原位置
            shakeTween.to(0.15, {
                x: originalPosition.x,
                y: originalPosition.y
            }, { easing: 'backOut' })
                .start();
        }, randomDelay);
    };
    /**
     * 显示所有隐藏的格子（游戏结束时调用）
     */
    SingleChessBoardController.prototype.showAllHiddenGrids = function () {
        if (!this.currentBoardNode) {
            console.error("棋盘节点未设置，无法显示隐藏格子！");
            return;
        }
        // 遍历棋盘的所有子节点，找到小格子并显示
        for (var i = 0; i < this.currentBoardNode.children.length; i++) {
            var child = this.currentBoardNode.children[i];
            // 如果是小格子节点
            if (child.name.startsWith("Grid_") || child.name === "block") {
                // 停止所有可能正在进行的动画
                child.stopAllActions();
                // 恢复显示状态
                child.active = true;
                child.opacity = 255;
                child.scaleX = 1;
                child.scaleY = 1;
                child.angle = 0; // 重置旋转角度
                // 恢复原始位置（如果有保存的话）
                if (child['originalPosition']) {
                    child.setPosition(child['originalPosition']);
                }
                // 确保格子可以交互
                var button = child.getComponent(cc.Button);
                if (button) {
                    button.enabled = true;
                }
            }
        }
    };
    /**
     * 清除所有预制体（游戏结束时调用）
     */
    SingleChessBoardController.prototype.clearAllPrefabs = function () {
        if (!this.currentBoardNode) {
            console.error("棋盘节点未设置，无法清除预制体！");
            return;
        }
        var childrenToRemove = [];
        // 遍历棋盘的所有子节点
        for (var i = 0; i < this.currentBoardNode.children.length; i++) {
            var child = this.currentBoardNode.children[i];
            // 检查是否是预制体（通过名称判断）
            if (child.name === "Biaoji" || child.name === "Boom" || child.name.startsWith("Boom")) {
                childrenToRemove.push(child);
            }
        }
        // 移除找到的预制体
        childrenToRemove.forEach(function (child) {
            child.removeFromParent();
        });
        // 重置格子数据
        this.reinitializeBoardData();
    };
    /**
     * 重新初始化棋盘数据（仅在开始新游戏时调用）
     */
    SingleChessBoardController.prototype.reinitializeBoardData = function () {
        if (!this.currentBoardConfig)
            return;
        // 重置gridData中的预制体状态
        for (var x = 0; x < this.currentBoardConfig.cols; x++) {
            for (var y = 0; y < this.currentBoardConfig.rows; y++) {
                if (this.gridData[x] && this.gridData[x][y]) {
                    this.gridData[x][y].hasPlayer = false;
                    this.gridData[x][y].playerNode = null;
                }
            }
        }
    };
    /**
     * 处理ExtendLevelInfo消息（游戏结束时调用）
     */
    SingleChessBoardController.prototype.onExtendLevelInfo = function () {
        this.showAllHiddenGrids();
        this.clearAllPrefabs();
    };
    /**
     * 处理LevelGameEnd消息（游戏结束时调用）
     * 注意：不清理任何数据，保持玩家的游玩痕迹
     */
    SingleChessBoardController.prototype.onLevelGameEnd = function () {
        // 不显示隐藏的格子，保持当前状态
        // 不清理预制体，不重置格子状态，保持游戏结果显示
        // 让玩家可以看到自己的标记（biaoji）、挖掘结果（数字、boom）等
    };
    /**
     * 检查是否点到了炸弹
     * @returns 是否点到了炸弹
     */
    SingleChessBoardController.prototype.hasBombExplodedInThisGame = function () {
        return this.hasBombExploded;
    };
    /**
     * 重置炸弹爆炸状态（开始新游戏时调用）
     */
    SingleChessBoardController.prototype.resetBombExplodedStatus = function () {
        this.hasBombExploded = false;
    };
    /**
     * 隐藏指定位置的格子（点击时调用）
     */
    SingleChessBoardController.prototype.hideGridAt = function (x, y) {
        if (!this.isValidCoordinate(x, y)) {
            console.warn("\u9690\u85CF\u683C\u5B50\u5931\u8D25\uFF1A\u5750\u6807(" + x + ", " + y + ")\u65E0\u6548");
            return;
        }
        // 获取格子节点
        var gridNode = this.gridNodes[x] && this.gridNodes[x][y];
        if (gridNode) {
            // 使用动画隐藏格子
            cc.tween(gridNode)
                .to(0.3, { opacity: 0, scaleX: 0, scaleY: 0 }, { easing: 'sineIn' })
                .call(function () {
                gridNode.active = false;
            })
                .start();
        }
    };
    /**
     * 获取当前棋盘类型
     */
    SingleChessBoardController.prototype.getCurrentBoardType = function () {
        return this.currentBoardType;
    };
    /**
     * 获取当前棋盘配置
     */
    SingleChessBoardController.prototype.getCurrentBoardConfig = function () {
        return this.currentBoardConfig;
    };
    /**
     * 处理点击响应，根据服务器返回的结果更新棋盘 - 参考联机版的地图更新逻辑
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param result 点击结果 ("boom" | "safe" | number)
     */
    SingleChessBoardController.prototype.handleClickResponse = function (x, y, result) {
        if (!this.isValidCoordinate(x, y)) {
            console.warn("\u5904\u7406\u70B9\u51FB\u54CD\u5E94\u5931\u8D25\uFF1A\u5750\u6807(" + x + ", " + y + ")\u65E0\u6548");
            return;
        }
        // 如果格子上有biaoji预制体，先移除它
        if (this.hasBiaojiAt(x, y)) {
            // 直接移除，不播放动画
            var gridData = this.gridData[x][y];
            if (gridData.playerNode) {
                gridData.playerNode.removeFromParent();
                gridData.playerNode = null;
            }
        }
        // 标记格子已被处理，防止重复点击
        this.gridData[x][y].hasPlayer = true;
        // 使用连锁动画的方式处理单个格子，保持一致性
        this.playGridDisappearAnimation(x, y, result);
    };
    /**
     * 处理连锁展开结果
     * @param floodFillResults 连锁展开数据数组
     */
    SingleChessBoardController.prototype.handleFloodFillResults = function (floodFillResults) {
        var _this = this;
        // 同时播放所有格子的消失动画，不使用延迟
        floodFillResults.forEach(function (gridResult) {
            var x = gridResult.x, y = gridResult.y, neighborMines = gridResult.neighborMines;
            if (!_this.isValidCoordinate(x, y)) {
                console.warn("\u8FDE\u9501\u5C55\u5F00\u8DF3\u8FC7\u65E0\u6548\u5750\u6807: (" + x + ", " + y + ")");
                return;
            }
            // 立即播放动画，不延迟
            _this.playGridDisappearAnimation(x, y, neighborMines);
        });
    };
    /**
     * 批量处理连锁反应的格子（参考联机版的processFloodFillResult）
     * @param revealedGrids 被揭开的格子列表 {x: number, y: number, neighborMines: number}[]
     */
    SingleChessBoardController.prototype.handleChainReaction = function (revealedGrids) {
        var _this = this;
        if (!revealedGrids || revealedGrids.length === 0) {
            return;
        }
        // 同时播放所有连锁格子的消失动画，不使用延迟
        revealedGrids.forEach(function (block) {
            // 立即播放动画，不延迟
            _this.playGridDisappearAnimation(block.x, block.y, block.neighborMines);
        });
    };
    /**
     * 播放格子消失动画（连锁效果）- 参考联机版ChessBoardController
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param neighborMines 周围地雷数量或结果类型（可以是数字、"mine"、"boom"等）
     */
    SingleChessBoardController.prototype.playGridDisappearAnimation = function (x, y, neighborMines) {
        var _this = this;
        // 如果格子上有biaoji预制体，先移除它（连锁展开时）
        if (this.hasBiaojiAt(x, y)) {
            var gridData = this.gridData[x][y];
            if (gridData.playerNode) {
                gridData.playerNode.removeFromParent();
                gridData.playerNode = null;
            }
        }
        // 标记格子已被处理（对于连锁格子）
        if (this.isValidCoordinate(x, y)) {
            this.gridData[x][y].hasPlayer = true;
        }
        // 先删除格子
        this.removeGridAt(x, y);
        // 延迟0.3秒后显示数字（等格子消失动画完成）
        // 使用带标识的延迟任务，方便重置时清理
        var delayCallback = function () {
            _this.updateNeighborMinesDisplay(x, y, neighborMines);
        };
        this.scheduleOnce(delayCallback, 0.3);
    };
    /**
     * 隐藏指定位置的格子（不销毁，以便重置时可以重新显示）- 参考联机版
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param immediate 是否立即隐藏（不播放动画）
     */
    SingleChessBoardController.prototype.removeGridAt = function (x, y, immediate) {
        if (immediate === void 0) { immediate = false; }
        if (!this.isValidCoordinate(x, y)) {
            console.warn("\u9690\u85CF\u683C\u5B50\u5931\u8D25\uFF1A\u5750\u6807(" + x + ", " + y + ")\u65E0\u6548");
            return;
        }
        // 获取格子节点
        var gridNode = this.gridNodes[x] && this.gridNodes[x][y];
        if (gridNode) {
            if (immediate) {
                // 立即隐藏，不播放动画
                gridNode.active = false;
            }
            else {
                // 播放四边形格子消失动画
                this.playGridFallAnimation(gridNode);
            }
        }
    };
    /**
     * 播放四边形格子消失动画
     * 效果：格子持续旋转，给一个随机向上的力，然后旋转着自由落体
     * @param gridNode 格子节点
     */
    SingleChessBoardController.prototype.playGridFallAnimation = function (gridNode) {
        if (!gridNode)
            return;
        // 停止该格子上所有正在进行的动画（包括震动动画）
        gridNode.stopAllActions();
        // 保存格子的原始位置（用于重置时恢复）
        if (!gridNode['originalPosition']) {
            gridNode['originalPosition'] = gridNode.getPosition();
        }
        // 保存原始的zIndex，用于动画结束后恢复
        var originalZIndex = gridNode.zIndex;
        // 设置更高的层级，确保下落的格子在数字预制体和其他元素之上
        gridNode.zIndex = 1000;
        // 随机选择向上的力的方向：0=向上，1=右上15度，2=左上15度
        var forceDirection = Math.floor(Math.random() * 3);
        var moveX = 0;
        var moveY = 200; // 向上的基础距离（增加高度）
        switch (forceDirection) {
            case 0: // 向上
                moveX = 0;
                break;
            case 1: // 右上15度
                moveX = Math.sin(20 * Math.PI / 180) * moveY;
                break;
            case 2: // 左上15度
                moveX = -Math.sin(20 * Math.PI / 180) * moveY;
                break;
        }
        // 随机旋转速度
        var rotationSpeed = (Math.random() * 1440 + 720) * (Math.random() > 0.5 ? 1 : -1); // 360-1080度/秒，随机方向
        // 动画参数
        var upTime = 0.15; // 向上运动时间
        var fallTime = 0.3; // 下落时间
        var initialPosition = gridNode.getPosition();
        // 创建持续旋转的动画
        var rotationTween = cc.tween(gridNode)
            .repeatForever(cc.tween().by(0.1, { angle: rotationSpeed * 0.1 }));
        // 创建分阶段的运动动画
        var movementTween = cc.tween(gridNode)
            // 第一阶段：向上抛出
            .to(upTime, {
            x: initialPosition.x + moveX,
            y: initialPosition.y + moveY
        }, { easing: 'quadOut' })
            // 第二阶段：自由落体
            .to(fallTime, {
            x: initialPosition.x + moveX + (Math.random() - 0.5) * 100,
            y: initialPosition.y - 500 // 下落到屏幕下方更远处
        }, { easing: 'quadIn' })
            .call(function () {
            // 动画结束后隐藏格子
            gridNode.active = false;
            // 停止旋转动画
            gridNode.stopAllActions();
            // 恢复原始的zIndex（虽然格子已经隐藏，但保持一致性）
            gridNode.zIndex = originalZIndex;
        });
        // 同时开始旋转和移动动画
        rotationTween.start();
        movementTween.start();
    };
    /**
     * 更新指定位置的neighborMines显示（使用boom数字预制体）- 参考联机版
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @param neighborMines 周围地雷数量或结果类型
     */
    SingleChessBoardController.prototype.updateNeighborMinesDisplay = function (x, y, neighborMines) {
        if (neighborMines === "boom" || neighborMines === "mine") {
            // 踩到地雷，生成boom预制体并触发震动
            this.createBoomPrefab(x, y, true); // true表示是当前用户踩到的雷，需要震动
            // 设置标记，表示点到了炸弹
            this.hasBombExploded = true;
        }
        else if (typeof neighborMines === "number" && neighborMines > 0) {
            // 显示数字
            this.createNumberPrefab(x, y, neighborMines);
        }
        else {
            // 如果是0、"safe"或其他，则不显示任何预制体
        }
    };
    /**
     * 移除指定位置的biaoji预制体
     * @param x 格子x坐标
     * @param y 格子y坐标
     */
    SingleChessBoardController.prototype.removeBiaojiAt = function (x, y) {
        if (!this.isValidCoordinate(x, y)) {
            return;
        }
        var gridData = this.gridData[x][y];
        if (gridData.hasPlayer && gridData.playerNode && gridData.playerNode.name === "Biaoji") {
            // 播放消失动画
            cc.tween(gridData.playerNode)
                .to(0.2, { scaleX: 0, scaleY: 0 }, { easing: 'sineIn' })
                .call(function () {
                gridData.playerNode.removeFromParent();
                gridData.hasPlayer = false;
                gridData.playerNode = null;
            })
                .start();
        }
    };
    /**
     * 检查指定位置是否有biaoji预制体
     * @param x 格子x坐标
     * @param y 格子y坐标
     * @returns 是否有biaoji预制体
     */
    SingleChessBoardController.prototype.hasBiaojiAt = function (x, y) {
        if (!this.isValidCoordinate(x, y)) {
            return false;
        }
        var gridData = this.gridData[x][y];
        return gridData.hasPlayer && gridData.playerNode && gridData.playerNode.name === "Biaoji";
    };
    /**
     * 获取所有biaoji的位置
     * @returns biaoji位置数组
     */
    SingleChessBoardController.prototype.getAllBiaojiPositions = function () {
        var positions = [];
        if (!this.currentBoardConfig)
            return positions;
        for (var x = 0; x < this.currentBoardConfig.cols; x++) {
            for (var y = 0; y < this.currentBoardConfig.rows; y++) {
                if (this.hasBiaojiAt(x, y)) {
                    positions.push({ x: x, y: y });
                }
            }
        }
        return positions;
    };
    /**
     * 重置棋盘到初始状态
     */
    SingleChessBoardController.prototype.resetBoard = function () {
        var _this = this;
        // 清理所有延迟任务（重要：防止上一局的连锁动画影响新游戏）
        this.unscheduleAllCallbacks();
        // 重置炸弹爆炸状态
        this.resetBombExplodedStatus();
        // 清除所有预制体
        this.clearAllPrefabs();
        // 重新初始化棋盘数据
        this.reinitializeBoardData();
        // 显示所有格子
        this.showAllHiddenGrids();
        // 重新启用触摸事件
        this.scheduleOnce(function () {
            _this.enableTouchForExistingGrids();
        }, 0.1);
    };
    /**
     * 禁用所有格子的触摸事件（游戏结束时调用）
     */
    SingleChessBoardController.prototype.disableAllGridTouch = function () {
        if (!this.currentBoardConfig)
            return;
        for (var x = 0; x < this.currentBoardConfig.cols; x++) {
            for (var y = 0; y < this.currentBoardConfig.rows; y++) {
                var gridNode = this.gridNodes[x] && this.gridNodes[x][y];
                if (gridNode) {
                    gridNode.off(cc.Node.EventType.TOUCH_START);
                    gridNode.off(cc.Node.EventType.TOUCH_END);
                    gridNode.off(cc.Node.EventType.TOUCH_CANCEL);
                }
            }
        }
    };
    /**
     * 启用所有格子的触摸事件
     */
    SingleChessBoardController.prototype.enableAllGridTouch = function () {
        this.enableTouchForExistingGrids();
    };
    __decorate([
        property(cc.Prefab)
    ], SingleChessBoardController.prototype, "boomPrefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], SingleChessBoardController.prototype, "biaojiPrefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], SingleChessBoardController.prototype, "boom1Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], SingleChessBoardController.prototype, "boom2Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], SingleChessBoardController.prototype, "boom3Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], SingleChessBoardController.prototype, "boom4Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], SingleChessBoardController.prototype, "boom5Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], SingleChessBoardController.prototype, "boom6Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], SingleChessBoardController.prototype, "boom7Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], SingleChessBoardController.prototype, "boom8Prefab", void 0);
    __decorate([
        property(cc.Node)
    ], SingleChessBoardController.prototype, "qipan8x8Node", void 0);
    __decorate([
        property(cc.Node)
    ], SingleChessBoardController.prototype, "qipan8x9Node", void 0);
    __decorate([
        property(cc.Node)
    ], SingleChessBoardController.prototype, "qipan9x9Node", void 0);
    __decorate([
        property(cc.Node)
    ], SingleChessBoardController.prototype, "qipan9x10Node", void 0);
    __decorate([
        property(cc.Node)
    ], SingleChessBoardController.prototype, "qipan10x10Node", void 0);
    SingleChessBoardController = __decorate([
        ccclass
    ], SingleChessBoardController);
    return SingleChessBoardController;
}(cc.Component));
exports.default = SingleChessBoardController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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