
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/game/AIManagedDialogController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, '423f58DLGJCFIMOAtc3Vt+Q', 'AIManagedDialogController');
// scripts/game/AIManagedDialogController.ts

"use strict";
// AI托管中页面控制器
// 当玩家进入AI托管状态时显示，点击屏幕任何位置发送取消托管消息
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var WebSocketManager_1 = require("../net/WebSocketManager");
var MessageId_1 = require("../net/MessageId");
var Config_1 = require("../util/Config");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var AIManagedDialogController = /** @class */ (function (_super) {
    __extends(AIManagedDialogController, _super);
    function AIManagedDialogController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.boardBg = null; // 背景板
        _this.maskNode = null; // 遮罩节点，用于接收点击事件
        _this.isShowing = false; // 是否正在显示
        return _this;
    }
    AIManagedDialogController.prototype.start = function () {
        console.log("AIManagedDialogController start() 被调用");
        // 初始化时隐藏
        this.node.active = false;
        // 如果 boardBg 没有设置，尝试查找子节点
        if (!this.boardBg) {
            var bgNode = this.node.getChildByName('bot_bg');
            if (bgNode) {
                this.boardBg = bgNode;
            }
            else {
                console.warn("未找到 bot_bg 子节点");
            }
        }
        // 为遮罩节点添加点击事件监听
        if (this.maskNode) {
            // 检查是否有 BlockInputEvents 组件，如果有则禁用它
            var blockInputEvents = this.maskNode.getComponent(cc.BlockInputEvents);
            if (blockInputEvents) {
                blockInputEvents.enabled = false;
            }
            this.maskNode.on(cc.Node.EventType.TOUCH_END, this.onMaskClick, this);
        }
        else {
            console.warn("maskNode 未设置");
        }
        // 同时为主节点添加点击事件监听作为备用
        this.node.on(cc.Node.EventType.TOUCH_END, this.onNodeClick, this);
    };
    /**
     * 显示托管中页面
     */
    AIManagedDialogController.prototype.show = function () {
        console.log("AIManagedDialogController.show() 被调用");
        if (this.isShowing) {
            console.log("托管页面已经在显示中，跳过");
            return;
        }
        this.isShowing = true;
        this.node.active = true;
        // 禁用 BlockInputEvents 组件以允许点击事件
        if (this.maskNode) {
            var blockInputEvents = this.maskNode.getComponent(cc.BlockInputEvents);
            if (blockInputEvents) {
                blockInputEvents.enabled = false;
                console.log("显示时禁用 BlockInputEvents 组件");
            }
        }
        console.log("托管页面节点已激活，node.active =", this.node.active);
        // 初始化动画状态
        if (this.boardBg) {
            this.boardBg.scale = 0;
            this.boardBg.opacity = 0;
            console.log("背景板初始化完成");
        }
        else {
            console.warn("❌ boardBg 节点未设置");
        }
        // 执行显示动画
        this.playShowAnimation();
        console.log("托管页面显示动画已启动");
    };
    /**
     * 隐藏托管中页面
     */
    AIManagedDialogController.prototype.hide = function () {
        var _this = this;
        if (!this.isShowing) {
            return;
        }
        this.isShowing = false;
        // 重新启用 BlockInputEvents 组件（如果存在）
        if (this.maskNode) {
            var blockInputEvents = this.maskNode.getComponent(cc.BlockInputEvents);
            if (blockInputEvents) {
                blockInputEvents.enabled = true;
                console.log("重新启用 BlockInputEvents 组件");
            }
        }
        // 执行隐藏动画
        this.playHideAnimation(function () {
            _this.node.active = false;
        });
    };
    /**
     * 播放显示动画
     */
    AIManagedDialogController.prototype.playShowAnimation = function () {
        console.log("playShowAnimation 开始执行");
        if (!this.boardBg) {
            console.warn("❌ boardBg 为空，无法播放动画");
            return;
        }
        console.log("开始播放托管页面显示动画，动画时间:", Config_1.Config.dialogScaleTime);
        // 缩放和透明度动画
        cc.tween(this.boardBg)
            .to(Config_1.Config.dialogScaleTime, {
            scale: 1,
            opacity: 255
        }, {
            easing: 'backOut'
        })
            .call(function () {
            console.log("托管页面显示动画完成");
        })
            .start();
    };
    /**
     * 播放隐藏动画
     * @param callback 动画完成回调
     */
    AIManagedDialogController.prototype.playHideAnimation = function (callback) {
        if (!this.boardBg) {
            if (callback)
                callback();
            return;
        }
        // 缩放和透明度动画
        cc.tween(this.boardBg)
            .to(Config_1.Config.dialogScaleTime, {
            scale: 0,
            opacity: 0
        }, {
            easing: 'backIn'
        })
            .call(function () {
            if (callback)
                callback();
        })
            .start();
    };
    /**
     * 遮罩点击事件处理
     */
    AIManagedDialogController.prototype.onMaskClick = function () {
        console.log("遮罩节点被点击");
        this.handleClick();
    };
    /**
     * 主节点点击事件处理
     */
    AIManagedDialogController.prototype.onNodeClick = function () {
        console.log("主节点被点击");
        this.handleClick();
    };
    /**
     * 统一的点击处理逻辑
     */
    AIManagedDialogController.prototype.handleClick = function () {
        if (!this.isShowing) {
            console.log("托管页面未显示，忽略点击");
            return;
        }
        console.log("托管页面点击，发送取消AI托管消息");
        // 发送取消AI托管消息
        this.sendCancelAIManagement();
        // 立即隐藏页面（不等待服务器响应）
        this.hide();
    };
    /**
     * 发送取消AI托管消息
     */
    AIManagedDialogController.prototype.sendCancelAIManagement = function () {
        var cancelData = {
        // 可以根据需要添加其他参数
        };
        WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeCancelAIManagement, cancelData);
        console.log("已发送取消AI托管消息");
    };
    /**
     * 检查是否正在显示
     */
    AIManagedDialogController.prototype.isVisible = function () {
        return this.isShowing && this.node.active;
    };
    AIManagedDialogController.prototype.onDestroy = function () {
        // 清理事件监听
        if (this.maskNode) {
            this.maskNode.off(cc.Node.EventType.TOUCH_END, this.onMaskClick, this);
        }
        if (this.node) {
            this.node.off(cc.Node.EventType.TOUCH_END, this.onNodeClick, this);
        }
    };
    __decorate([
        property(cc.Node)
    ], AIManagedDialogController.prototype, "boardBg", void 0);
    __decorate([
        property(cc.Node)
    ], AIManagedDialogController.prototype, "maskNode", void 0);
    AIManagedDialogController = __decorate([
        ccclass
    ], AIManagedDialogController);
    return AIManagedDialogController;
}(cc.Component));
exports.default = AIManagedDialogController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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