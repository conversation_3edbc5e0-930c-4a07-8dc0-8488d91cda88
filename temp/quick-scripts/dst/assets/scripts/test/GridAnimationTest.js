
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/test/GridAnimationTest.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'a5fb7yX0MxJzJhk2Z9NhWXz', 'GridAnimationTest');
// scripts/test/GridAnimationTest.ts

"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var GridAnimationTest = /** @class */ (function (_super) {
    __extends(GridAnimationTest, _super);
    function GridAnimationTest() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.testSquareAnimationBtn = null;
        _this.testHexAnimationBtn = null;
        _this.resetGridsBtn = null;
        _this.statusLabel = null;
        return _this;
    }
    GridAnimationTest.prototype.onLoad = function () {
        // 设置按钮点击事件
        if (this.testSquareAnimationBtn) {
            this.testSquareAnimationBtn.node.on('click', this.testSquareGridAnimation, this);
        }
        if (this.testHexAnimationBtn) {
            this.testHexAnimationBtn.node.on('click', this.testHexGridAnimation, this);
        }
        if (this.resetGridsBtn) {
            this.resetGridsBtn.node.on('click', this.resetAllGrids, this);
        }
    };
    /**
     * 测试四边形格子消失动画
     */
    GridAnimationTest.prototype.testSquareGridAnimation = function () {
        this.updateStatus("测试四边形格子消失动画...");
        // 获取棋盘控制器实例
        var chessBoardController = window.chessBoardController;
        var singleChessBoardController = window.singleChessBoardController;
        if (chessBoardController) {
            // 测试联机版棋盘
            this.updateStatus("触发联机版格子消失动画");
            // 随机选择一个格子进行动画测试
            var x = Math.floor(Math.random() * 8);
            var y = Math.floor(Math.random() * 8);
            chessBoardController.removeGridAt(x, y, false);
            this.updateStatus("\u8054\u673A\u7248\u683C\u5B50(" + x + ", " + y + ")\u6D88\u5931\u52A8\u753B\u5DF2\u89E6\u53D1");
        }
        else if (singleChessBoardController) {
            // 测试单人版棋盘
            this.updateStatus("触发单人版格子消失动画");
            // 随机选择一个格子进行动画测试
            var x = Math.floor(Math.random() * 8);
            var y = Math.floor(Math.random() * 8);
            singleChessBoardController.removeGridAt(x, y, false);
            this.updateStatus("\u5355\u4EBA\u7248\u683C\u5B50(" + x + ", " + y + ")\u6D88\u5931\u52A8\u753B\u5DF2\u89E6\u53D1");
        }
        else {
            this.updateStatus("未找到棋盘控制器实例");
        }
    };
    /**
     * 测试六边形格子消失动画
     */
    GridAnimationTest.prototype.testHexGridAnimation = function () {
        this.updateStatus("测试六边形格子消失动画...");
        // 获取六边形棋盘控制器实例
        var hexChessBoardController = window.hexChessBoardController;
        if (hexChessBoardController) {
            this.updateStatus("触发六边形格子消失动画");
            // 随机选择一个六边形格子进行动画测试
            var q = Math.floor(Math.random() * 5) - 2; // -2 到 2
            var r = Math.floor(Math.random() * 5) - 2; // -2 到 2
            hexChessBoardController.hideHexGridAt(q, r, false);
            this.updateStatus("\u516D\u8FB9\u5F62\u683C\u5B50(" + q + ", " + r + ")\u6D88\u5931\u52A8\u753B\u5DF2\u89E6\u53D1");
        }
        else {
            this.updateStatus("未找到六边形棋盘控制器实例");
        }
    };
    /**
     * 重置所有格子
     */
    GridAnimationTest.prototype.resetAllGrids = function () {
        this.updateStatus("重置所有格子...");
        // 获取所有棋盘控制器实例
        var chessBoardController = window.chessBoardController;
        var singleChessBoardController = window.singleChessBoardController;
        var hexChessBoardController = window.hexChessBoardController;
        var resetCount = 0;
        if (chessBoardController && chessBoardController.resetGameScene) {
            chessBoardController.resetGameScene();
            resetCount++;
        }
        if (singleChessBoardController && singleChessBoardController.resetBoard) {
            singleChessBoardController.resetBoard();
            resetCount++;
        }
        if (hexChessBoardController && hexChessBoardController.resetGameScene) {
            hexChessBoardController.resetGameScene();
            resetCount++;
        }
        if (resetCount > 0) {
            this.updateStatus("\u5DF2\u91CD\u7F6E " + resetCount + " \u4E2A\u68CB\u76D8\u7684\u683C\u5B50");
        }
        else {
            this.updateStatus("未找到可重置的棋盘控制器");
        }
    };
    /**
     * 更新状态显示
     */
    GridAnimationTest.prototype.updateStatus = function (message) {
        if (this.statusLabel) {
            this.statusLabel.string = message;
        }
        console.log("[GridAnimationTest] " + message);
    };
    __decorate([
        property(cc.Button)
    ], GridAnimationTest.prototype, "testSquareAnimationBtn", void 0);
    __decorate([
        property(cc.Button)
    ], GridAnimationTest.prototype, "testHexAnimationBtn", void 0);
    __decorate([
        property(cc.Button)
    ], GridAnimationTest.prototype, "resetGridsBtn", void 0);
    __decorate([
        property(cc.Label)
    ], GridAnimationTest.prototype, "statusLabel", void 0);
    GridAnimationTest = __decorate([
        ccclass
    ], GridAnimationTest);
    return GridAnimationTest;
}(cc.Component));
exports.default = GridAnimationTest;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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