"use strict";
cc._RF.push(module, '423f58DLGJCFIMOAtc3Vt+Q', 'AIManagedDialogController');
// scripts/game/AIManagedDialogController.ts

"use strict";
// AI托管中页面控制器
// 当玩家进入AI托管状态时显示，点击屏幕任何位置发送取消托管消息
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var WebSocketManager_1 = require("../net/WebSocketManager");
var MessageId_1 = require("../net/MessageId");
var Config_1 = require("../util/Config");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var AIManagedDialogController = /** @class */ (function (_super) {
    __extends(AIManagedDialogController, _super);
    function AIManagedDialogController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.boardBg = null; // 背景板
        _this.maskNode = null; // 遮罩节点，用于接收点击事件
        _this.isShowing = false; // 是否正在显示
        return _this;
    }
    AIManagedDialogController.prototype.start = function () {
        console.log("AIManagedDialogController start() 被调用");
        // 初始化时隐藏
        this.node.active = false;
        // 如果 boardBg 没有设置，尝试查找子节点
        if (!this.boardBg) {
            var bgNode = this.node.getChildByName('bot_bg');
            if (bgNode) {
                this.boardBg = bgNode;
            }
            else {
                console.warn("未找到 bot_bg 子节点");
            }
        }
        // 为遮罩节点添加点击事件监听
        if (this.maskNode) {
            // 检查是否有 BlockInputEvents 组件，如果有则禁用它
            var blockInputEvents = this.maskNode.getComponent(cc.BlockInputEvents);
            if (blockInputEvents) {
                blockInputEvents.enabled = false;
            }
            this.maskNode.on(cc.Node.EventType.TOUCH_END, this.onMaskClick, this);
        }
        else {
            console.warn("maskNode 未设置");
        }
        // 同时为主节点添加点击事件监听作为备用
        this.node.on(cc.Node.EventType.TOUCH_END, this.onNodeClick, this);
    };
    /**
     * 显示托管中页面
     */
    AIManagedDialogController.prototype.show = function () {
        console.log("AIManagedDialogController.show() 被调用");
        if (this.isShowing) {
            console.log("托管页面已经在显示中，跳过");
            return;
        }
        this.isShowing = true;
        this.node.active = true;
        // 禁用 BlockInputEvents 组件以允许点击事件
        if (this.maskNode) {
            var blockInputEvents = this.maskNode.getComponent(cc.BlockInputEvents);
            if (blockInputEvents) {
                blockInputEvents.enabled = false;
                console.log("显示时禁用 BlockInputEvents 组件");
            }
        }
        console.log("托管页面节点已激活，node.active =", this.node.active);
        // 初始化动画状态
        if (this.boardBg) {
            this.boardBg.scale = 0;
            this.boardBg.opacity = 0;
            console.log("背景板初始化完成");
        }
        else {
            console.warn("❌ boardBg 节点未设置");
        }
        // 执行显示动画
        this.playShowAnimation();
        console.log("托管页面显示动画已启动");
    };
    /**
     * 隐藏托管中页面
     */
    AIManagedDialogController.prototype.hide = function () {
        var _this = this;
        if (!this.isShowing) {
            return;
        }
        this.isShowing = false;
        // 重新启用 BlockInputEvents 组件（如果存在）
        if (this.maskNode) {
            var blockInputEvents = this.maskNode.getComponent(cc.BlockInputEvents);
            if (blockInputEvents) {
                blockInputEvents.enabled = true;
                console.log("重新启用 BlockInputEvents 组件");
            }
        }
        // 执行隐藏动画
        this.playHideAnimation(function () {
            _this.node.active = false;
        });
    };
    /**
     * 播放显示动画
     */
    AIManagedDialogController.prototype.playShowAnimation = function () {
        console.log("playShowAnimation 开始执行");
        if (!this.boardBg) {
            console.warn("❌ boardBg 为空，无法播放动画");
            return;
        }
        console.log("开始播放托管页面显示动画，动画时间:", Config_1.Config.dialogScaleTime);
        // 缩放和透明度动画
        cc.tween(this.boardBg)
            .to(Config_1.Config.dialogScaleTime, {
            scale: 1,
            opacity: 255
        }, {
            easing: 'backOut'
        })
            .call(function () {
            console.log("托管页面显示动画完成");
        })
            .start();
    };
    /**
     * 播放隐藏动画
     * @param callback 动画完成回调
     */
    AIManagedDialogController.prototype.playHideAnimation = function (callback) {
        if (!this.boardBg) {
            if (callback)
                callback();
            return;
        }
        // 缩放和透明度动画
        cc.tween(this.boardBg)
            .to(Config_1.Config.dialogScaleTime, {
            scale: 0,
            opacity: 0
        }, {
            easing: 'backIn'
        })
            .call(function () {
            if (callback)
                callback();
        })
            .start();
    };
    /**
     * 遮罩点击事件处理
     */
    AIManagedDialogController.prototype.onMaskClick = function () {
        console.log("遮罩节点被点击");
        this.handleClick();
    };
    /**
     * 主节点点击事件处理
     */
    AIManagedDialogController.prototype.onNodeClick = function () {
        console.log("主节点被点击");
        this.handleClick();
    };
    /**
     * 统一的点击处理逻辑
     */
    AIManagedDialogController.prototype.handleClick = function () {
        if (!this.isShowing) {
            console.log("托管页面未显示，忽略点击");
            return;
        }
        console.log("托管页面点击，发送取消AI托管消息");
        // 发送取消AI托管消息
        this.sendCancelAIManagement();
        // 立即隐藏页面（不等待服务器响应）
        this.hide();
    };
    /**
     * 发送取消AI托管消息
     */
    AIManagedDialogController.prototype.sendCancelAIManagement = function () {
        var cancelData = {
        // 可以根据需要添加其他参数
        };
        WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeCancelAIManagement, cancelData);
        console.log("已发送取消AI托管消息");
    };
    /**
     * 检查是否正在显示
     */
    AIManagedDialogController.prototype.isVisible = function () {
        return this.isShowing && this.node.active;
    };
    AIManagedDialogController.prototype.onDestroy = function () {
        // 清理事件监听
        if (this.maskNode) {
            this.maskNode.off(cc.Node.EventType.TOUCH_END, this.onMaskClick, this);
        }
        if (this.node) {
            this.node.off(cc.Node.EventType.TOUCH_END, this.onNodeClick, this);
        }
    };
    __decorate([
        property(cc.Node)
    ], AIManagedDialogController.prototype, "boardBg", void 0);
    __decorate([
        property(cc.Node)
    ], AIManagedDialogController.prototype, "maskNode", void 0);
    AIManagedDialogController = __decorate([
        ccclass
    ], AIManagedDialogController);
    return AIManagedDialogController;
}(cc.Component));
exports.default = AIManagedDialogController;

cc._RF.pop();