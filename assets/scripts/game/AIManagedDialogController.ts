// AI托管中页面控制器
// 当玩家进入AI托管状态时显示，点击屏幕任何位置发送取消托管消息

import { WebSocketManager } from "../net/WebSocketManager";
import { MessageId } from "../net/MessageId";
import { Config } from "../util/Config";

const { ccclass, property } = cc._decorator;

@ccclass
export default class AIManagedDialogController extends cc.Component {

    @property(cc.Node)
    boardBg: cc.Node = null; // 背景板

    @property(cc.Node)
    maskNode: cc.Node = null; // 遮罩节点，用于接收点击事件

   

    private isShowing: boolean = false; // 是否正在显示

    start() {
        console.log("AIManagedDialogController start() 被调用");

        // 初始化时隐藏
        this.node.active = false;

        

        // 如果 boardBg 没有设置，尝试查找子节点
        if (!this.boardBg) {
         
            const bgNode = this.node.getChildByName('bot_bg');
            if (bgNode) {
                this.boardBg = bgNode;
              
            } else {
                console.warn("未找到 bot_bg 子节点");
            }
        }

        // 为遮罩节点添加点击事件监听
        if (this.maskNode) {
            // 检查是否有 BlockInputEvents 组件，如果有则禁用它
            const blockInputEvents = this.maskNode.getComponent(cc.BlockInputEvents);
            if (blockInputEvents) {
               
                blockInputEvents.enabled = false;
            }

            this.maskNode.on(cc.Node.EventType.TOUCH_END, this.onMaskClick, this);
           
        } else {
            console.warn("maskNode 未设置");
        }

        // 同时为主节点添加点击事件监听作为备用
        this.node.on(cc.Node.EventType.TOUCH_END, this.onNodeClick, this);
    }

    /**
     * 显示托管中页面
     */
    show() {
        console.log("AIManagedDialogController.show() 被调用");

        if (this.isShowing) {
            console.log("托管页面已经在显示中，跳过");
            return;
        }

      
        this.isShowing = true;
        this.node.active = true;

        // 禁用 BlockInputEvents 组件以允许点击事件
        if (this.maskNode) {
            const blockInputEvents = this.maskNode.getComponent(cc.BlockInputEvents);
            if (blockInputEvents) {
                blockInputEvents.enabled = false;
                console.log("显示时禁用 BlockInputEvents 组件");
            }
        }

        console.log("托管页面节点已激活，node.active =", this.node.active);

        // 初始化动画状态
        if (this.boardBg) {
            this.boardBg.scale = 0;
            this.boardBg.opacity = 0;
            console.log("背景板初始化完成");
        } else {
            console.warn("❌ boardBg 节点未设置");
        }

        // 执行显示动画
        this.playShowAnimation();
        console.log("托管页面显示动画已启动");
    }

    /**
     * 隐藏托管中页面
     */
    hide() {
        if (!this.isShowing) {
            return;
        }

        this.isShowing = false;

        // 重新启用 BlockInputEvents 组件（如果存在）
        if (this.maskNode) {
            const blockInputEvents = this.maskNode.getComponent(cc.BlockInputEvents);
            if (blockInputEvents) {
                blockInputEvents.enabled = true;
                console.log("重新启用 BlockInputEvents 组件");
            }
        }

        // 执行隐藏动画
        this.playHideAnimation(() => {
            this.node.active = false;
        });
    }

    /**
     * 播放显示动画
     */
    private playShowAnimation() {
        console.log("playShowAnimation 开始执行");

        if (!this.boardBg) {
            console.warn("❌ boardBg 为空，无法播放动画");
            return;
        }

        console.log("开始播放托管页面显示动画，动画时间:", Config.dialogScaleTime);

        // 缩放和透明度动画
        cc.tween(this.boardBg)
            .to(Config.dialogScaleTime, {
                scale: 1,
                opacity: 255
            }, {
                easing: 'backOut'
            })
            .call(() => {
                console.log("托管页面显示动画完成");
            })
            .start();
    }

    /**
     * 播放隐藏动画
     * @param callback 动画完成回调
     */
    private playHideAnimation(callback?: () => void) {
        if (!this.boardBg) {
            if (callback) callback();
            return;
        }

        // 缩放和透明度动画
        cc.tween(this.boardBg)
            .to(Config.dialogScaleTime, { 
                scale: 0, 
                opacity: 0 
            }, { 
                easing: 'backIn' 
            })
            .call(() => {
                if (callback) callback();
            })
            .start();
    }

    /**
     * 遮罩点击事件处理
     */
    private onMaskClick() {
        console.log("遮罩节点被点击");
        this.handleClick();
    }

    /**
     * 主节点点击事件处理
     */
    private onNodeClick() {
        console.log("主节点被点击");
        this.handleClick();
    }

    /**
     * 统一的点击处理逻辑
     */
    private handleClick() {
        if (!this.isShowing) {
            console.log("托管页面未显示，忽略点击");
            return;
        }

        console.log("托管页面点击，发送取消AI托管消息");

        // 发送取消AI托管消息
        this.sendCancelAIManagement();

        // 立即隐藏页面（不等待服务器响应）
        this.hide();
    }

    /**
     * 发送取消AI托管消息
     */
    private sendCancelAIManagement() {
        const cancelData = {
            // 可以根据需要添加其他参数
        };

        WebSocketManager.GetInstance().sendMsg(MessageId.MsgTypeCancelAIManagement, cancelData);
        console.log("已发送取消AI托管消息");
    }

    /**
     * 检查是否正在显示
     */
    isVisible(): boolean {
        return this.isShowing && this.node.active;
    }

    onDestroy() {
        // 清理事件监听
        if (this.maskNode) {
            this.maskNode.off(cc.Node.EventType.TOUCH_END, this.onMaskClick, this);
        }

        if (this.node) {
            this.node.off(cc.Node.EventType.TOUCH_END, this.onNodeClick, this);
        }
    }
}
