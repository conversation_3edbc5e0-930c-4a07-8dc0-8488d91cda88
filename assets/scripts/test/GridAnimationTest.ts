const { ccclass, property } = cc._decorator;

@ccclass
export default class GridAnimationTest extends cc.Component {

    @property(cc.Button)
    testSquareAnimationBtn: cc.Button = null;

    @property(cc.Button)
    testHexAnimationBtn: cc.Button = null;

    @property(cc.Button)
    resetGridsBtn: cc.Button = null;

    @property(cc.Label)
    statusLabel: cc.Label = null;

    onLoad() {
        // 设置按钮点击事件
        if (this.testSquareAnimationBtn) {
            this.testSquareAnimationBtn.node.on('click', this.testSquareGridAnimation, this);
        }

        if (this.testHexAnimationBtn) {
            this.testHexAnimationBtn.node.on('click', this.testHexGridAnimation, this);
        }

        if (this.resetGridsBtn) {
            this.resetGridsBtn.node.on('click', this.resetAllGrids, this);
        }
    }

    /**
     * 测试四边形格子消失动画
     */
    testSquareGridAnimation() {
        this.updateStatus("测试四边形格子消失动画...");
        
        // 获取棋盘控制器实例
        const chessBoardController = (window as any).chessBoardController;
        const singleChessBoardController = (window as any).singleChessBoardController;
        
        if (chessBoardController) {
            // 测试联机版棋盘
            this.updateStatus("触发联机版格子消失动画");
            // 随机选择一个格子进行动画测试
            const x = Math.floor(Math.random() * 8);
            const y = Math.floor(Math.random() * 8);
            chessBoardController.removeGridAt(x, y, false);
            this.updateStatus(`联机版格子(${x}, ${y})消失动画已触发`);
        } else if (singleChessBoardController) {
            // 测试单人版棋盘
            this.updateStatus("触发单人版格子消失动画");
            // 随机选择一个格子进行动画测试
            const x = Math.floor(Math.random() * 8);
            const y = Math.floor(Math.random() * 8);
            singleChessBoardController.removeGridAt(x, y, false);
            this.updateStatus(`单人版格子(${x}, ${y})消失动画已触发`);
        } else {
            this.updateStatus("未找到棋盘控制器实例");
        }
    }

    /**
     * 测试六边形格子消失动画
     */
    testHexGridAnimation() {
        this.updateStatus("测试六边形格子消失动画...");
        
        // 获取六边形棋盘控制器实例
        const hexChessBoardController = (window as any).hexChessBoardController;
        
        if (hexChessBoardController) {
            this.updateStatus("触发六边形格子消失动画");
            // 随机选择一个六边形格子进行动画测试
            const q = Math.floor(Math.random() * 5) - 2; // -2 到 2
            const r = Math.floor(Math.random() * 5) - 2; // -2 到 2
            hexChessBoardController.hideHexGridAt(q, r, false);
            this.updateStatus(`六边形格子(${q}, ${r})消失动画已触发`);
        } else {
            this.updateStatus("未找到六边形棋盘控制器实例");
        }
    }

    /**
     * 重置所有格子
     */
    resetAllGrids() {
        this.updateStatus("重置所有格子...");
        
        // 获取所有棋盘控制器实例
        const chessBoardController = (window as any).chessBoardController;
        const singleChessBoardController = (window as any).singleChessBoardController;
        const hexChessBoardController = (window as any).hexChessBoardController;
        
        let resetCount = 0;
        
        if (chessBoardController && chessBoardController.resetGameScene) {
            chessBoardController.resetGameScene();
            resetCount++;
        }
        
        if (singleChessBoardController && singleChessBoardController.resetBoard) {
            singleChessBoardController.resetBoard();
            resetCount++;
        }
        
        if (hexChessBoardController && hexChessBoardController.resetGameScene) {
            hexChessBoardController.resetGameScene();
            resetCount++;
        }
        
        if (resetCount > 0) {
            this.updateStatus(`已重置 ${resetCount} 个棋盘的格子`);
        } else {
            this.updateStatus("未找到可重置的棋盘控制器");
        }
    }

    /**
     * 更新状态显示
     */
    private updateStatus(message: string) {
        if (this.statusLabel) {
            this.statusLabel.string = message;
        }
        console.log(`[GridAnimationTest] ${message}`);
    }
}
