// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html

import { AudioManager } from "./AudioManager";
import { Config } from "./Config";

const { ccclass, property } = cc._decorator;


export class Tools {

    static setTouchEvent(peopleNode: cc.Node, startFunction?: Function, endFunction?: Function, cancelFunction?: Function) {
        this.setTouchEventParent(peopleNode, true, startFunction, endFunction, cancelFunction)
    }
    static setGameTouchEvent(peopleNode: cc.Node, startFunction?: Function, endFunction?: Function, cancelFunction?: Function) {
        this.setTouchEventParent(peopleNode, false, startFunction, endFunction, cancelFunction)
    }

    //添加点击事件 
    //isSound 是否需要按键音效，大厅的都需要 游戏内有自己的按键音所以不需要
    //peopleNode 节点
    //startFunction 按下事件
    //endFunction 抬起事件
    //cancelFunction 取消事件
    private static setTouchEventParent(peopleNode: cc.Node, isSound: boolean, startFunction?: Function, endFunction?: Function, cancelFunction?: Function) {
        peopleNode.on(cc.Node.EventType.TOUCH_START, (event: cc.Event.EventTouch) => {
            if (isSound) {
                AudioManager.keyingToneAudio();
            }

            if (startFunction != null) {
                startFunction(peopleNode, event);
            }
        }, this);
        peopleNode.on(cc.Node.EventType.TOUCH_END, (event: cc.Event.EventTouch) => {
            if (endFunction != null) {
                endFunction(peopleNode, event);
            }
        }, this);
        peopleNode.on(cc.Node.EventType.TOUCH_CANCEL, (event: cc.Event.EventTouch) => {
            if (cancelFunction != null) {
                cancelFunction(peopleNode, event);
            }
        }, this);
    }

    static cancelTouchStartListener(peopleNode: cc.Node) {
        peopleNode.off(cc.Node.EventType.TOUCH_START, this);
    }
    static cancelTouchEndListener(peopleNode: cc.Node) {
        peopleNode.off(cc.Node.EventType.TOUCH_END, this);
    }
    static cancelTouchCancelListener(peopleNode: cc.Node) {
        peopleNode.off(cc.Node.EventType.TOUCH_CANCEL, this);
    }

    //为精灵添加图片
    static setNodeSpriteFrame(node: cc.Node, path: string) {
        cc.resources.load(path, cc.SpriteFrame, function (error: Error, assets: cc.SpriteFrame) {
            const sprite = node.getComponent(cc.Sprite);
            sprite.spriteFrame = assets;
        });
    }

    //添加网络图片
    static setNodeSpriteFrameUrl(node: cc.Node, url: string) {
    

        if (!node) {

            return;
        }

        let avatarSp = node.getComponent(cc.Sprite);
        if (!avatarSp) {
            console.warn("⚠️ 节点没有Sprite组件，正在添加...");
            avatarSp = node.addComponent(cc.Sprite);
        }

        if (url == null || url == '') {
            console.warn("⚠️ URL为空，跳过图片加载");
            return;
        }

      

        // 根据URL判断文件扩展名
        let ext = '.png'; // 默认
        if (url.toLowerCase().includes('.jpg') || url.toLowerCase().includes('.jpeg')) {
            ext = '.jpg';
        } else if (url.toLowerCase().includes('.png')) {
            ext = '.png';
        }

       

        cc.assetManager.loadRemote(url, { ext: ext }, (err, texture: cc.Texture2D) => {
            if (err) {
                console.error(`❌ 图片加载失败: ${err.message || err}`);
                console.error(`❌ 失败的URL: ${url}`);

                // 尝试加载备用图片或设置默认颜色
                Tools.setFallbackTexture(avatarSp);
                return;
            }

            

            texture.setPremultiplyAlpha(true);  // 👈 关键设置
            texture.packable = false;//加载圆头像的时候 必须关闭合图
            avatarSp.spriteFrame = new cc.SpriteFrame(texture);

            // 确保节点可见
            node.active = true;
            node.opacity = 255;

           
        });
    }

    // 设置备用纹理
    static setFallbackTexture(sprite: cc.Sprite) {
       

        // 创建一个简单的纯色纹理
        let texture = new cc.Texture2D();
        let color = [150, 150, 150, 255]; // 灰色

        texture.initWithData(new Uint8Array(color), cc.Texture2D.PixelFormat.RGBA8888, 1, 1);
        sprite.spriteFrame = new cc.SpriteFrame(texture);

      
    }

    // 异步加载网络图片（带回调）
    static setNodeSpriteFrameUrlAsync(node: cc.Node, url: string, onComplete?: (success: boolean) => void) {
       

        if (!node) {
            console.error("❌ 节点为null，无法设置图片");
            if (onComplete) onComplete(false);
            return;
        }

        let avatarSp = node.getComponent(cc.Sprite);
        if (!avatarSp) {
            console.warn("⚠️ 节点没有Sprite组件，正在添加...");
            avatarSp = node.addComponent(cc.Sprite);
        }

        if (url == null || url == '') {
            console.warn("⚠️ URL为空，跳过图片加载");
            if (onComplete) onComplete(false);
            return;
        }

        // 根据URL判断文件扩展名
        let ext = '.png'; // 默认
        if (url.toLowerCase().includes('.jpg') || url.toLowerCase().includes('.jpeg')) {
            ext = '.jpg';
        } else if (url.toLowerCase().includes('.png')) {
            ext = '.png';
        }

      

        cc.assetManager.loadRemote(url, { ext: ext }, (err, texture: cc.Texture2D) => {
            if (err) {
                console.error(`❌ 图片加载失败: ${err.message || err}`);
                console.error(`❌ 失败的URL: ${url}`);

                // 尝试加载备用图片或设置默认颜色
                Tools.setFallbackTexture(avatarSp);
                if (onComplete) onComplete(false);
                return;
            }

          

            texture.setPremultiplyAlpha(true);  // 👈 关键设置
            texture.packable = false;//加载圆头像的时候 必须关闭合图
            avatarSp.spriteFrame = new cc.SpriteFrame(texture);

            // 确保节点可见
            node.active = true;
            node.opacity = 255;

            
            if (onComplete) onComplete(true);
        });
    }

    //红色按钮
    static redButton(node: cc.Node, click?: Function,label?:string) {
        Tools.buttonState(node, Config.btnRedNormal, Config.btnRedPressed, Config.btnRedNormalColor, Config.btnRedPressedColor, click,label);
    }
    //绿色按钮
    static greenButton(node: cc.Node, click?: Function,label?:string) {
        Tools.buttonState(node, Config.btnGreenNormal, Config.btnGreenPressed, Config.btnGreenNormalColor, Config.btnGreenPressedColor, click,label);
    }
    //黄色按钮
    static yellowButton(node: cc.Node, click?: Function,label?:string) {
        Tools.buttonState(node, Config.btnYellowNormal, Config.btnYellowPressed, Config.btnYellowNormalColor, Config.btnYellowPressedColor, click,label);
    }
    //灰色按钮
    static grayButton(node: cc.Node, click?: Function,label?:string) {
        Tools.buttonState(node, Config.btnGrayNormal, Config.btnGrayNormal, Config.btnGrayNormalColor, Config.btnGrayNormalColor, click,label);
    }


    //通用的按钮点击事件，带点击变颜色的
    static buttonState(node: cc.Node, normalImg: string, pressedImg: string, normalColor: string, pressedColor: string, click?: Function,labelText?:string) {

        let btnGreen = node.getChildByName('btn_color_normal');//获取按钮背景节点
        let btnLabel = node.getChildByName('button_label');//获取按钮文字节点

        let label = btnLabel.getComponent(cc.Label);
        let labelOutline = btnLabel.getComponent(cc.LabelOutline);

        if(labelText!=null){
            label.string = labelText
        }

        Tools.setTouchEvent(btnGreen, (node: cc.Node) => {
            Tools.setNodeSpriteFrame(node, pressedImg);
            label.fontSize = 34;
            label.lineHeight= 34
            let color = new cc.Color();
            cc.Color.fromHEX(color, pressedColor);
            labelOutline.color = color;
            btnLabel.color = cc.Color.fromHEX(new cc.Color(),'#B3B3B3');


        }, (node: cc.Node) => {
            Tools.setNodeSpriteFrame(node, normalImg);
            label.fontSize = 36;
            label.lineHeight= 36
            let color = new cc.Color();
            cc.Color.fromHEX(color, normalColor);
            labelOutline.color = color;
            btnLabel.color = cc.Color.fromHEX(new cc.Color(),'#FFFFFF');
            if (click != null) {
                click();
            }
        }, (node: cc.Node) => {
            Tools.setNodeSpriteFrame(node, normalImg);
            label.fontSize = 36;
            label.lineHeight= 36
            let color = new cc.Color();
            cc.Color.fromHEX(color, normalColor);
            labelOutline.color = color;
            btnLabel.color = cc.Color.fromHEX(new cc.Color(),'#FFFFFF');
        });
    }

    //点击变颜色的图片按钮
    static imageButtonClick(node: cc.Node, normalImg: string, pressedImg: string, click: Function) {
        Tools.setTouchEvent(node, (node: cc.Node) => {
            Tools.setNodeSpriteFrame(node, pressedImg);
        }, (node: cc.Node) => {
            Tools.setNodeSpriteFrame(node, normalImg);
            click()
        }, (node: cc.Node) => {
            Tools.setNodeSpriteFrame(node, normalImg);
        });
    }

    //格式化资金显示格式的
    static NumToTBMK(num: number, digit: number = 1, min: number = 10000): string {
        let intNum = num;
        if (intNum < min) {
            return intNum.toString();
        }

        let unitStrArr = ["T", "B", "M", "K"];
        let unitArr = [Math.pow(10, 12), Math.pow(10, 9), Math.pow(10, 6), Math.pow(10, 3)];
        for (let i = 0; i < unitArr.length; ++i) {
            let result = intNum / unitArr[i];
            if (result >= 1) {
                let str = result.toString();
                let strArr = str.split(".");
                let suffix = strArr[1] ?? "";
                if (suffix.length >= digit) {
                    if (digit == 0) {
                        return strArr[0] + unitStrArr[i];
                    }
                    return strArr[0] + "." + suffix.substring(0, digit) + unitStrArr[i];
                }
                else {
                    let fillStr = new Array(digit - suffix.length).fill("0").join("");
                    return strArr[0] + "." + suffix + fillStr + unitStrArr[i];
                }
            }
        }
    }

    static getCurrentTimeWithMilliseconds(): string {
        const currentDate = new Date();
        const year = currentDate.getFullYear();
        const month = String(currentDate.getMonth() + 1).padStart(2, '0');
        const day = String(currentDate.getDate()).padStart(2, '0');
        const hours = String(currentDate.getHours()).padStart(2, '0');
        const minutes = String(currentDate.getMinutes()).padStart(2, '0');
        const seconds = String(currentDate.getSeconds()).padStart(2, '0');
        const milliseconds = String(currentDate.getMilliseconds()).padStart(3, '0');

        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}.${milliseconds}`;
    }

    //赋值文本到剪切板
    static copyToClipboard(text: string) {
        const textarea = document.createElement('textarea');
        textarea.style.position = 'fixed';
        textarea.style.opacity = '0';
        textarea.value = text;
        document.body.appendChild(textarea);
        textarea.select();
        try {
            const successful = document.execCommand('copy');
            if (successful) {
                console.log('文本已复制到剪切板');
            } else {
                console.error('复制到剪切板失败');
            }
        } catch (err) {
            console.error('复制到剪切板失败：', err);
        }
        document.body.removeChild(textarea);
    }

    //拆分数组用的，一个长度为 10 的数组 拆分成 2 个长度为 5 的数组 chunkArray(user_s, 5)
    static chunkArray<T>(arr: T[], chunkSize: number): T[][] {
        const result: T[][] = [];
        for (let i = 0; i < arr.length; i += chunkSize) {
            result.push(arr.slice(i, i + chunkSize));
        }
        return result;
    }
    //设置倒计时的秒数的位置
    static setCountDownTimeLabel(buttonNode: cc.Node) {
        let btn = buttonNode.getChildByName('button_label')
        let timeBtn = buttonNode.getChildByName('buttonLabel_time')
        // buttonLabel_time 紧贴着 button_label 的右边
        // button_label的右边界 + buttonLabel_time宽度的一半 = buttonLabel_time的中心位置
        let xPos = btn.position.x + btn.width / 2 + timeBtn.width / 2
        timeBtn.setPosition(xPos, 0)
    }

}
